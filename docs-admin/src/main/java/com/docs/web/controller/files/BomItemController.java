package com.docs.web.controller.files;

import com.docs.common.core.controller.BaseController;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.BomItem;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.core.domain.vo.BomDrawingChangeHistoryDTO;
import com.docs.common.core.domain.vo.FileChangeHistoryDTO;
import com.docs.common.core.page.TableDataInfo;
import com.docs.common.jooq.generated.tables.pojos.BomItems;
import com.docs.common.enums.DrawingFileType;
import com.docs.files.service.BomDrawingChangeHistoryService;
import com.docs.files.service.BomItemService;
import com.docs.files.service.FileChangesService;
import com.docs.files.service.MaterialDrawingMapService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * BOM数据管理Controller
 */
@RestController
@RequestMapping("/files/bom")
@RequiredArgsConstructor
public class BomItemController extends BaseController {
    
    private final BomItemService bomItemService;
    private final FileChangesService fileChangesService;
    private final BomDrawingChangeHistoryService bomDrawingChangeHistoryService;
    private final MaterialDrawingMapService materialDrawingMapService;
    
    /**
     * 查询BOM数据列表
     */
    @PreAuthorize("@ss.hasPermi('files:list')")
    @GetMapping("/list")
    public TableDataInfo list(BomItem bomItem) {
        startPage();
        List<BomItems> list = bomItemService.selectBomItemList(bomItem);
        return getDataTable(list);
    }
    
    /**
     * 根据文件ID获取BOM数据
     */
    @PreAuthorize("@ss.hasPermi('files:list')")
    @GetMapping("/file/{fileId}")
    public AjaxResult getByFileId(@PathVariable Integer fileId) {
        List<BomItemsVO> list = bomItemService.selectByFileId(fileId);
        return AjaxResult.success(list);
    }

    /**
     * 上传BOM项图纸
     */
    @PreAuthorize("@ss.hasPermi('files:upload')")
    @PostMapping("/drawing/upload")
    public AjaxResult uploadDrawing(
            @RequestParam("file") MultipartFile file,
            @RequestParam("bomItemId") Integer bomItemId,
            @RequestParam("drawingNo") String drawingNo,
            @RequestParam("subType") String subType,
            @RequestParam(value = "remark", required = false) String remark) {

        if (file.isEmpty()) {
            return AjaxResult.error("请选择要上传的文件");
        }

        String filename = file.getOriginalFilename();
        if (filename == null || filename.isEmpty()) {
            return AjaxResult.error("文件名不能为空");
        }

        // 验证备注长度
        if (remark != null && remark.length() > 500) {
            return AjaxResult.error("备注内容不能超过500个字符");
        }

        return bomItemService.uploadDrawing(file, bomItemId, drawingNo, subType, remark);
    }

    /**
     * 上传BOM项多文件图纸
     */
    @PreAuthorize("@ss.hasPermi('files:upload')")
    @PostMapping("/drawing/upload/multiple")
    public AjaxResult uploadMultipleDrawings(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("bomItemId") Integer bomItemId,
            @RequestParam("drawingNo") String drawingNo,
            @RequestParam(value = "remark", required = false) String remark,
            @RequestParam Map<String, String> allParams) {

        if (files == null || files.length == 0) {
            return AjaxResult.error("请选择要上传的文件");
        }

        // 验证文件名
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                return AjaxResult.error("上传的文件不能为空");
            }
            String filename = file.getOriginalFilename();
            if (filename == null || filename.isEmpty()) {
                return AjaxResult.error("文件名不能为空");
            }
        }

        // 验证备注长度
        if (remark != null && remark.length() > 500) {
            return AjaxResult.error("备注内容不能超过500个字符");
        }

        // 从allParams中提取fileTypes数组
        List<String> fileTypesList = new ArrayList<>();
        for (int i = 0; i < files.length; i++) {
            String fileType = allParams.get("fileTypes[" + i + "]");
            if (fileType == null) {
                return AjaxResult.error("文件类型信息缺失，索引：" + i);
            }
            fileTypesList.add(fileType);
        }
        
        String[] fileTypes = fileTypesList.toArray(new String[0]);

        // 验证文件类型数组长度
        if (fileTypes.length != files.length) {
            return AjaxResult.error("文件类型信息与文件数量不匹配");
        }

        return bomItemService.uploadMultipleDrawings(files, bomItemId, drawingNo, fileTypes, remark);
    }
    
    /**
     * 查询BOM项图纸变更历史（旧版本，已废弃）
     * @deprecated 请使用 getDrawingChangeHistoryV2
     */
    @Deprecated
    @PreAuthorize("@ss.hasPermi('files:changeHistory')")
    @GetMapping("/drawing/history/{bomItemId}")
    public TableDataInfo getDrawingChangeHistory(@PathVariable Integer bomItemId) {
        startPage();
        List<FileChangeHistoryDTO> list = fileChangesService.selectBomItemDrawingChangesList(bomItemId);
        return getDataTable(list);
    }
    
    /**
     * 查询BOM项图纸变更历史（新版本）
     * 支持4种文件类型的完整变更历史追踪
     *
     * @param bomItemId BOM项ID
     * @param fileType 文件类型过滤参数（可选）：
     *                 - 单个类型：PDF、2D、3D、COMPRESSED、RD_FILE
     *                 - 特殊值：DRAWING_ONLY（排除RD_FILE，只查询图纸类型）
     */
    @PreAuthorize("@ss.hasPermi('files:changeHistory')")
    @GetMapping("/drawing/history/v2/{bomItemId}")
    public TableDataInfo getDrawingChangeHistoryV2(@PathVariable Integer bomItemId,
                                                   @RequestParam(value = "fileType", required = false) String fileType) {

        logger.info("查询BOM项变更历史: bomItemId={}, fileType={}", bomItemId, fileType);

        try {
            if (bomItemId == null) {
                return getDataTable(new ArrayList<>());
            }

            startPage();
            List<BomDrawingChangeHistoryDTO> list;

            if (fileType != null && !fileType.trim().isEmpty()) {
                if ("DRAWING_ONLY".equalsIgnoreCase(fileType)) {
                    // 特殊处理：只查询图纸类型，排除研发文件
                    list = bomDrawingChangeHistoryService.selectDrawingOnlyChangeHistory(bomItemId);
                } else {
                    // 按单个文件类型过滤查询
                    try {
                        DrawingFileType drawingFileType = DrawingFileType.valueOf(fileType.toUpperCase());
                        list = bomDrawingChangeHistoryService.selectBomDrawingChangeHistoryByType(bomItemId, drawingFileType);
                    } catch (IllegalArgumentException e) {
                        logger.warn("无效的文件类型参数: {}", fileType);
                        list = bomDrawingChangeHistoryService.selectBomDrawingChangeHistory(bomItemId);
                    }
                }
            } else {
                // 查询所有类型的变更历史
                list = bomDrawingChangeHistoryService.selectBomDrawingChangeHistory(bomItemId);
            }

            return getDataTable(list);

        } catch (Exception e) {
            logger.error("查询BOM项变更历史失败: bomItemId={}, fileType={}", bomItemId, fileType, e);
            return getDataTable(new ArrayList<>());
        }
    }
    /**
     * 更新图纸编号
     */
    @PreAuthorize("@ss.hasPermi('files:upload')")
    @PostMapping("/drawing/update-number")
    public AjaxResult updateDrawingNumber(@RequestBody Map<String, String> params) {
        String materialCode = params.get("materialCode");
        String newDrawingNo = params.get("newDrawingNo");
        
        if (materialCode == null || materialCode.trim().isEmpty()) {
            return AjaxResult.error("物料编码不能为空");
        }
        
        if (newDrawingNo == null || newDrawingNo.trim().isEmpty()) {
            return AjaxResult.error("新图纸编号不能为空");
        }
        
        try {
            materialDrawingMapService.updateDrawingNumber(materialCode.trim(), newDrawingNo.trim());
            return AjaxResult.success("图纸编号更新成功");
        } catch (Exception e) {
            logger.error("更新图纸编号失败: materialCode={}, newDrawingNo={}", materialCode, newDrawingNo, e);
            return AjaxResult.error("更新图纸编号失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取物料已存在的文件类型
     */
    @PreAuthorize("@ss.hasPermi('files:list')")
    @GetMapping("/drawing/existing-types/{materialCode}")
    public AjaxResult getExistingFileTypes(@PathVariable String materialCode) {
        if (materialCode == null || materialCode.trim().isEmpty()) {
            return AjaxResult.error("物料编码不能为空");
        }
        
        try {
            List<String> existingTypes = materialDrawingMapService.getExistingFileTypes(materialCode.trim());
            return AjaxResult.success("查询成功", existingTypes);
        } catch (Exception e) {
            logger.error("查询已存在文件类型失败: materialCode={}", materialCode, e);
            return AjaxResult.error("查询已存在文件类型失败: " + e.getMessage());
        }
    }

    // ========================= 研发文件管理接口 =========================
    
    /**
     * 上传研发文件
     * 如果BOM项已存在研发文件，将直接覆盖原文件并记录变更历史
     */
    @PreAuthorize("@ss.hasPermi('files:rd:upload')")
    @PostMapping("/uploadRdFile")
    public AjaxResult uploadRdFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("bomItemId") Integer bomItemId,
            @RequestParam(value = "reason", required = false) String reason) {
        
        logger.info("开始上传研发文件: bomItemId={}, fileName={}, reason={}", 
                   bomItemId, file.getOriginalFilename(), reason);
        
        try {
            // 参数验证
            if (file.isEmpty()) {
                return AjaxResult.error("文件不能为空");
            }
            
            if (bomItemId == null) {
                return AjaxResult.error("BOM项ID不能为空");
            }
            
            // 调用服务层上传研发文件
            return bomItemService.uploadRdFile(file, bomItemId, reason);
            
        } catch (Exception e) {
            logger.error("上传研发文件失败: bomItemId={}, fileName={}", 
                        bomItemId, file.getOriginalFilename(), e);
            return AjaxResult.error("上传研发文件失败: " + e.getMessage());
        }
    }
    

    
    /**
     * 获取研发文件变更历史
     */
    @PreAuthorize("@ss.hasPermi('files:rd:history')")
    @GetMapping("/rdFileHistory/{bomItemId}")
    public AjaxResult getRdFileHistory(@PathVariable Integer bomItemId) {
        
        logger.info("查询研发文件变更历史: bomItemId={}", bomItemId);
        
        try {
            if (bomItemId == null) {
                return AjaxResult.error("BOM项ID不能为空");
            }
            
            return bomItemService.getRdFileChangeHistory(bomItemId);
            
        } catch (Exception e) {
            logger.error("查询研发文件变更历史失败: bomItemId={}", bomItemId, e);
            return AjaxResult.error("查询研发文件变更历史失败: " + e.getMessage());
        }
    }

//
//    /**
//     * 重新解析BOM文件
//     */
//    @PreAuthorize("@ss.hasPermi('files:edit')")
//    @PostMapping("/reparse/{fileId}")
//    public AjaxResult reparseFile(@PathVariable Integer fileId, @RequestParam String filePath, @RequestParam String bomType) {
//        int rows = bomItemService.parseBomExcelAndSave(fileId, filePath, bomType);
//        return AjaxResult.success("重新解析成功，共解析" + rows + "条数据");
//    }
} 