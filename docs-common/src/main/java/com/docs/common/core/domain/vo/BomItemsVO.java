package com.docs.common.core.domain.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

@Data
public class BomItemsVO {

    private Integer       id;
    private Integer       fileId;
    private String        itemNo;
    private Integer       modelId;
    private Integer       depth;
    private String        materialCode;
    private String        materialName;
    private String        specification;
    private String        supplier;
    private String        unit;
    private BigDecimal    quantity;
    private String        productionProperty;
    private String        category;
    private String        remark;
    private String        bomType;
    private String        drawingNo;       // 图纸编号
    private Integer       drawingFileId;   // 图纸文件ID（向后兼容）
    
    // 多文件支持字段
    private Integer       pdfFileId;       // PDF文件ID
    private Integer       drawing2dFileId; // 2D图纸文件ID
    private Integer       drawing3dFileId; // 3D图纸文件ID
    private Integer       compressedFileId; // 压缩包文件ID
    
    // 文件名显示字段
    private String        pdfFileName;     // PDF文件名
    private String        drawing2dFileName; // 2D图纸文件名
    private String        drawing3dFileName; // 3D图纸文件名
    private String        compressedFileName; // 压缩包文件名
    
    // 研发文件支持字段
    private Integer       rdFileId;        // 研发文件ID
    private String        rdFileName;      // 研发文件名
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // Checklist Item 1: Add children field for hierarchy
    private List<BomItemsVO> children = new ArrayList<>();
}
