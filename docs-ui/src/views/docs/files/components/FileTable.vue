<template>
  <div>
    <el-table v-loading="loading" :data="fileList" row-key="id" stripe :row-class-name="getRowClassName">
      <el-table-column type="expand">
        <template slot-scope="props">
          <bom-items-table
            v-if="props.row.fileType === 'BOM'"
            :fileId="props.row.id"
            :modelCode="props.row.modelCode"
            :bomItems="props.row.bomItems || []"
            @upload-drawing="onUploadDrawing"
            @download-drawing="onDownloadDrawing"
            @view-history="handleHistory"
            @download-rd-file="onDownloadRdFile"
            @upload-rd-file="onUploadRdFile"
            @view-rd-file-history="onViewRdFileHistory" />
        </template>
      </el-table-column>
      <el-table-column label="产品型号" width="150" align="center" prop="modelCode" />
      <el-table-column label="文件类型" width="150" align="center" prop="fileType">
        <template slot-scope="scope">
          <span>{{ formatFileType(scope.row.fileType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="子类型" width="150" align="center" prop="subType">
        <template slot-scope="scope">
          <span>{{ formatFileType(scope.row.subType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件名称" align="center" width="500">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.filePath" placement="top">
            <span v-if="scope.row.fileName" class="file-name-cell">{{ scope.row.fileName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="图纸编号" align="center" prop="drawingNo" />
      <el-table-column label="版本" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.version">{{ scope.row.version }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'ACTIVE'" style="color: #67C23A; background-color: #F0F9EB;">使用</el-tag>
          <el-tag v-if="scope.row.status === 'OBSOLETE'" style="color: #909399; background-color: #F5F7FA;">已报废</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" prop="uploadTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.filePath" content="下载" placement="top">
            <el-button
              v-hasPermi="['files:download']"
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleDownload(scope.row)"
            />
          </el-tooltip>
          <el-tooltip content="历史" placement="">
            <el-button
             v-if="scope.row.hasChanges"
              v-hasPermi="['files:changeHistory']"
              size="mini"
              type="text"
              icon="el-icon-time"
              @click="(event) => handleHistory(scope.row, event)"
            />
          </el-tooltip>
          <el-tooltip content="变更" placement="top">
            <el-button
              v-if="scope.row.status === 'ACTIVE'"
              v-hasPermi="['files:changeVersion']"
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="handleChange(scope.row)"
            />
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-hasPermi="['files:delete']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="handlePagination"
    />

    <!-- 版本变更对话框 -->
    <file-version-dialog
      :visible.sync="versionDialogVisible"
      :file="selectedFile"
      @submit="submitVersionChange"
    />

    <!-- 历史记录对话框 -->
    <file-history-dialog
      :visible.sync="historyDialogVisible"
      :file="selectedFile"
      @fetch-history="fetchHistory"
      @download="downloadHistoryFile"
    />
  </div>
</template>

<script>
import BomItemsTable from './BomItemsTable'
import FileVersionDialog from './FileVersionDialog'
import FileHistoryDialog from './FileHistoryDialog'
import { formatFileType } from '../utils/fileUtils'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'FileTable',
  components: {
    BomItemsTable,
    FileVersionDialog,
    FileHistoryDialog
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    fileList: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    statusOptions: {
      type: Array,
      default: () => []
    },
    highlightFileId: {
      type: [String, Number],
      default: null
    },
    shouldHighlight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      // 版本变更对话框
      versionDialogVisible: false,
      selectedFile: null,
      // 历史记录对话框
      historyDialogVisible: false
    }
  },
  methods: {
    // 使用工具函数格式化文件类型
    formatFileType,
    // 获取行的类名，用于高亮显示
    getRowClassName({ row }) {
      if (this.shouldHighlight && this.highlightFileId && row.id == this.highlightFileId) {
        return 'highlighted-row';
      }
      return '';
    },
    handlePagination(val) {
      this.$emit('pagination', {
        pageNum: val.page,
        pageSize: val.limit
      })
    },
    handleDownload(row) {
      this.$emit('download', row)
    },
    handleHistory(row, event) {
      console.log('FileTable - handleHistory 被调用，参数:', row);
      // 阻止事件冒泡，避免触发父组件的handleHistory方法
      if (event) {
        event.stopPropagation()
      }

      // 如果row是数字，说明是bomItemId，需要创建一个对象
      if (typeof row === 'number' || (typeof row === 'string' && !isNaN(row))) {
        // 创建一个对象，包含id和hasChanges属性
        row = {
          id: row,
          hasChanges: true,
          fileName: 'BOM物料图纸'
        };
      }

      // 打开历史记录对话框
      this.selectedFile = row
      console.log('设置 selectedFile:', row);
      this.historyDialogVisible = true
      console.log('将historyDialogVisible设置为true，应该打开历史记录对话框');

      // 通知父组件已处理了历史记录点击事件
      this.$emit('history-handled')
    },
    handleChange(row) {
      this.selectedFile = row
      this.versionDialogVisible = true
    },
    handleDelete(row) {
      this.$emit('delete', row)
    },
    onUploadDrawing(row, modelCode) {
      this.$emit('upload-drawing', row, modelCode)
    },
    onDownloadDrawing(drawingFileId) {
      this.$emit('download-drawing', drawingFileId)
    },

    onViewBomItemHistory(bomItemId) {
      console.log('FileTable - onViewBomItemHistory 被调用，bomItemId:', bomItemId);
      this.$emit('bom-item-history', bomItemId)
    },

    // ========================= 研发文件事件处理 =========================
    
    // 下载研发文件
    onDownloadRdFile(fileId) {
      console.log('FileTable - onDownloadRdFile 被调用，fileId:', fileId);
      this.$emit('download-rd-file', fileId)
    },
    
    // 上传研发文件
    onUploadRdFile(row) {
      console.log('FileTable - onUploadRdFile 被调用，row:', row);
      this.$emit('upload-rd-file', row)
    },
    
    // 查看研发文件历史
    onViewRdFileHistory(row) {
      console.log('FileTable - onViewRdFileHistory 被调用，row:', row);
      this.$emit('view-rd-file-history', row)
    },
    // 提交版本变更
    submitVersionChange(formData, callback) {
      this.$emit('change', formData, callback)
    },

    // 获取历史记录
    fetchHistory(params, callback) {
      // 使用新的事件名称发送事件
      this.$emit('history-fetch', params, callback)
    },

    // 下载历史版本文件
    downloadHistoryFile(fileInfo) {
      this.$emit('download', fileInfo)
    },

    // 刷新BOM数据
    refreshBomData() {
      // 查找所有展开的BOM行，并刷新它们的数据
      this.$nextTick(() => {
        // 获取当前展开的行 - 使用更可靠的方法查找BomItemsTable组件
        const bomTables = [];
        
        // 递归查找所有BomItemsTable组件
        const findBomTables = (component) => {
          if (component.$options.name === 'BomItemsTable') {
            bomTables.push(component);
          }
          if (component.$children) {
            component.$children.forEach(child => findBomTables(child));
          }
        };
        
        findBomTables(this);
        
        // 重新加载每个BOM表的数据
        bomTables.forEach(bomTable => {
          if (bomTable.loadBomItems) {
            console.log('正在刷新BOM数据...', bomTable.fileId);
            bomTable.loadBomItems();
          }
        });
        
        console.log(`找到 ${bomTables.length} 个BOM表格，已触发刷新`);
      });
    }
  }
}
</script>

<style scoped>
.file-name-cell {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

<style>
/* 高亮行样式 - 不使用scoped，确保能影响el-table内部元素 */
.highlighted-row {
  background-color: #ecf5ff !important; /* 浅蓝色背景 */
  transition: background-color 0.5s;
  animation: highlight-pulse 2s infinite;
}

@keyframes highlight-pulse {
  0% { background-color: #ecf5ff; }
  50% { background-color: #d9ecff; }
  100% { background-color: #ecf5ff; }
}

/* 确保斑马纹样式不会覆盖高亮样式 */
.el-table--striped .el-table__body tr.highlighted-row td {
  background-color: inherit !important;
}
</style>
