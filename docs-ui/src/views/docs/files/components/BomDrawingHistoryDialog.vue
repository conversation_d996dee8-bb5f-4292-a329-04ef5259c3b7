<template>
  <el-dialog
    :title="getDialogTitle()"
    :visible.sync="visible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose">
    
    <div v-if="loading" style="text-align: center; padding: 40px;">
      <i class="el-icon-loading"></i>
      <span style="margin-left: 10px;">正在加载变更历史...</span>
    </div>
    
    <div v-else>
      <!-- 头部信息 -->
      <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
        <h4 style="margin: 0 0 10px 0; color: #409EFF;">物料信息</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <strong>物料编码:</strong> {{ materialInfo.materialCode || '-' }}
          </el-col>
          <el-col :span="8">
            <strong>物料名称:</strong> {{ materialInfo.materialName || '-' }}
          </el-col>
          <el-col :span="10">
            <strong>规格型号:</strong> {{ materialInfo.specification || '-' }}
          </el-col>
        </el-row>
      </div>

      <!-- 筛选条件 -->
      <div style="margin-bottom: 15px;">
        <el-form :inline="true" size="small">
          <el-form-item label="文件类型:">
            <el-select v-model="filterFileType" placeholder="全部" clearable @change="handleFilterChange">
              <el-option label="全部" value=""></el-option>
              <el-option label="PDF文档" value="PDF"></el-option>
              <el-option label="2D图纸" value="2D"></el-option>
              <el-option label="3D图纸" value="3D"></el-option>
              <el-option label="2D/3D压缩包" value="COMPRESSED"></el-option>
              <el-option label="研发文件" value="RD_FILE"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="变更类型:">
            <el-select v-model="filterChangeType" placeholder="全部" clearable @change="handleFilterChange">
              <el-option label="全部" value=""></el-option>
              <el-option label="新增" value="ADD"></el-option>
              <el-option label="更新" value="UPDATE"></el-option>
              <el-option label="删除" value="DELETE"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 变更历史表格 -->
      <el-table
        :data="filteredHistoryList"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        max-height="500">
        
        <el-table-column prop="changedAt" label="变更时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.changedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="fileTypeDisplay" label="文件类型" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getFileTypeTagType(scope.row.fileType)" size="small">
              {{ scope.row.fileTypeDisplay }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="changeTypeDisplay" label="变更类型" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="getChangeTypeTagType(scope.row.changeType)" size="small">
              {{ scope.row.changeTypeDisplay }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column v-if="!isRdFileType" label="变更前图纸编号" width="140" align="center">
          <template slot-scope="scope">
            <span style="color: #909399;">{{ scope.row.oldDrawingNo || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column v-if="!isRdFileType" label="变更后图纸编号" width="140" align="center">
          <template slot-scope="scope">
            <span style="color: #67C23A; font-weight: 500;">{{ scope.row.newDrawingNo || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="变更前文件名" min-width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.oldFileName" style="color: #909399;">
              <el-tooltip :content="scope.row.oldFileName" placement="top">
                <span class="file-name-text">{{ scope.row.oldFileName }}</span>
              </el-tooltip>
            </div>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="变更后文件名" min-width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.newFileName" style="color: #67C23A; font-weight: 500;">
              <el-tooltip :content="scope.row.newFileName" placement="top">
                <a href="javascript:;" 
                   class="clickable-file-name" 
                   @click="handleDownloadFile(scope.row.newFileId)">
                  {{ scope.row.newFileName }}
                </a>
              </el-tooltip>
            </div>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="changedByName" label="变更人" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="info" size="mini">{{ scope.row.changedByName }}</el-tag>
          </template>
        </el-table-column>
        
        <!-- <el-table-column prop="changeReason" label="变更原因" min-width="150" align="left">
          <template slot-scope="scope">
            <div v-if="scope.row.changeReason" class="change-reason">
              <el-tooltip :content="scope.row.changeReason" placement="top" :disabled="scope.row.changeReason.length <= 20">
                <span>{{ scope.row.changeReason.length > 20 ? scope.row.changeReason.substring(0, 20) + '...' : scope.row.changeReason }}</span>
              </el-tooltip>
            </div>
            <span v-else style="color: #C0C4CC;">-</span>
          </template>
        </el-table-column>
         -->
      </el-table>
      
      <!-- 分页 -->
      <div style="text-align: center; margin-top: 20px;" v-if="historyList.length > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredHistoryList.length">
        </el-pagination>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!loading && historyList.length === 0" style="text-align: center; padding: 40px;">
        <el-empty description="暂无变更历史记录" :image-size="100"></el-empty>
      </div>
    </div>
    
  </el-dialog>
</template>

<script>
import { getBomItemDrawingChangesV2 } from '@/api/files'

export default {
  name: 'BomDrawingHistoryDialog',
  props: {
    fileType: {
      type: String,
      default: 'drawing' // 'drawing' 或 'rdFile'
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      historyList: [],
      materialInfo: {
        materialCode: '',
        materialName: '',
        specification: ''
      },
      filterFileType: '',
      filterChangeType: '',
      pagination: {
        currentPage: 1,
        pageSize: 10
      },
      // 使用内部数据属性而不是直接修改 prop
      currentFileType: this.fileType
    }
  },
  computed: {
    // 判断是否为研发文件类型
    isRdFileType() {
      return this.currentFileType === 'rdFile'
    },
    filteredHistoryList() {
      let filtered = this.historyList
      
      // 按文件类型筛选
      if (this.filterFileType) {
        filtered = filtered.filter(item => item.fileType === this.filterFileType)
      }
      
      // 按变更类型筛选
      if (this.filterChangeType) {
        filtered = filtered.filter(item => item.changeType === this.filterChangeType)
      }
      
      return filtered
    }
  },
  watch: {
    // 监听 prop 变化，同步到内部数据属性
    fileType: {
      handler(newVal) {
        this.currentFileType = newVal
      },
      immediate: true
    }
  },
  methods: {
    // 显示对话框
    show(bomItem, fileType = 'drawing') {
      this.visible = true
      // 如果传入了文件类型参数，更新内部数据属性
      if (fileType && fileType !== this.currentFileType) {
        this.currentFileType = fileType
      }
      this.materialInfo = {
        materialCode: bomItem.materialCode || '',
        materialName: bomItem.materialName || '',
        specification: bomItem.specification || ''
      }
      this.loadHistoryData(bomItem.id)
    },
    
    // 加载历史数据
    async loadHistoryData(bomItemId) {
      this.loading = true
      try {
        let response
        if (this.isRdFileType) {
          // 调用统一的变更历史API，传递RD_FILE文件类型参数
          response = await getBomItemDrawingChangesV2(bomItemId, { fileType: 'RD_FILE' })
        } else {
          // 调用图纸变更历史API，不传递文件类型参数（查询所有图纸类型）
          response = await getBomItemDrawingChangesV2(bomItemId)
        }

        this.historyList = response.rows || response.data || []

        // 为每个记录添加显示文本
        this.historyList.forEach(record => {
          record.fileTypeDisplay = this.getFileTypeDisplay(record.fileType)
          record.changeTypeDisplay = this.getChangeTypeDisplay(record.changeType)
        })

      } catch (error) {
        console.error('加载变更历史失败:', error)
        this.$message.error('加载变更历史失败')
        this.historyList = []
      } finally {
        this.loading = false
      }
    },
    
    // 获取对话框标题
    getDialogTitle() {
      return this.isRdFileType ? 'BOM研发文件变更历史' : 'BOM图纸变更历史'
    },
    
    // 获取文件类型显示文本
    getFileTypeDisplay(fileType) {
      const typeMap = {
        'PDF': 'PDF文档',
        '2D': '2D图纸',
        '3D': '3D图纸',
        'COMPRESSED': '压缩包',
        'RD_FILE': '研发文件'
      }
      return typeMap[fileType] || fileType
    },
    
    // 获取变更类型显示文本
    getChangeTypeDisplay(changeType) {
      const typeMap = {
        'ADD': '新增',
        'UPDATE': '更新',
        'DELETE': '删除'
      }
      return typeMap[changeType] || changeType
    },
    
    // 获取文件类型标签颜色
    getFileTypeTagType(fileType) {
      const colorMap = {
        'PDF': 'danger',
        '2D': 'primary',
        '3D': 'success',
        'COMPRESSED': 'warning',
        'RD_FILE': 'info'
      }
      return colorMap[fileType] || 'info'
    },
    
    // 获取变更类型标签颜色
    getChangeTypeTagType(changeType) {
      const colorMap = {
        'ADD': 'success',
        'UPDATE': 'primary',
        'DELETE': 'danger'
      }
      return colorMap[changeType] || 'info'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    // 下载文件
    handleDownloadFile(fileId) {
      if (!fileId) return
      this.$emit('download-file', fileId)
    },
    
    // 筛选条件变更
    handleFilterChange() {
      this.pagination.currentPage = 1
    },
    
    // 分页大小变更
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
    },
    
    // 当前页变更
    handleCurrentChange(page) {
      this.pagination.currentPage = page
    },
    
    // 关闭对话框
    handleClose() {
      this.visible = false
      this.historyList = []
      this.materialInfo = {
        materialCode: '',
        materialName: '',
        specification: ''
      }
      this.filterFileType = ''
      this.filterChangeType = ''
      this.pagination = {
        currentPage: 1,
        pageSize: 10
      }
      // 重置文件类型为默认值
      this.currentFileType = this.fileType
    }
  }
}
</script>

<style scoped>
.file-name-text {
  max-width: 100px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.clickable-file-name {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
  max-width: 100px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.clickable-file-name:hover {
  color: #66b1ff;
}

.change-reason {
  line-height: 1.4;
  word-break: break-word;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-dialog__body {
  padding: 20px;
}
</style>