<template>
  <div>
    <div v-if="bomItems && bomItems.length > 0" style="padding: 10px 20px;">
      <el-table
        :data="bomItems"
        row-key="id"
        default-expand-all
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        border
        stripe
        highlight-current-row
        size="large"
        fit
        style="width: 100%"
        overflow-x: auto>
      <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column prop="materialName" label="产品名称" width="220" align="left"></el-table-column>
        <el-table-column prop="materialCode" label="产品编码" width="120" align="center"></el-table-column>
        <el-table-column prop="specification" label="产品型号/规格" width="600" align="left"></el-table-column>
        <el-table-column label="图纸编号" width="200" align="center">
          <template slot-scope="scope">
            <div class="drawing-no-cell">
              <span>{{ scope.row.drawingNo || '-' }}</span>
              <el-tooltip v-if="scope.row.drawingNo && !(scope.row.children && scope.row.children.length)" content="修改图纸编号" placement="top">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  class="edit-drawing-no-btn"
                  @click="handleEditDrawingNumber(scope.row)"
                ></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        
        <!-- PDF文件列 -->
        <el-table-column label="PDF" width="150" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.pdfFileId && scope.row.pdfFileName" content="点击下载PDF文档" placement="top">
              <a
                href="javascript:;"
                class="clickable-file-name"
                @click="handleDownloadFile(scope.row.pdfFileId)">
                {{ scope.row.pdfFileName }}
              </a>
            </el-tooltip>
            <span v-else class="no-file">-</span>
          </template>
        </el-table-column>
        
        <!-- 2D图纸列 -->
        <el-table-column label="2D图纸" width="150" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.drawing2dFileId && scope.row.drawing2dFileName" content="点击下载2D图纸" placement="top">
              <a
                href="javascript:;"
                class="clickable-file-name"
                @click="handleDownloadFile(scope.row.drawing2dFileId)">
                {{ scope.row.drawing2dFileName }}
              </a>
            </el-tooltip>
            <span v-else class="no-file">-</span>
          </template>
        </el-table-column>
        
        <!-- 3D图纸列 -->
        <el-table-column label="3D图纸" width="120" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.drawing3dFileId && scope.row.drawing3dFileName" content="点击下载3D图纸" placement="top">
              <a
                href="javascript:;"
                class="clickable-file-name"
                @click="handleDownloadFile(scope.row.drawing3dFileId)">
                {{ scope.row.drawing3dFileName }}
              </a>
            </el-tooltip>
            <span v-else class="no-file">-</span>
          </template>
        </el-table-column>
        
        <!-- 2D/3D压缩包列 -->
        <el-table-column label="2D/3D压缩包" width="120" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.compressedFileId && scope.row.compressedFileName" content="点击下载压缩包" placement="top">
              <a
                href="javascript:;"
                class="clickable-file-name"
                @click="handleDownloadFile(scope.row.compressedFileId)">
                {{ scope.row.compressedFileName }}
              </a>
            </el-tooltip>
            <span v-else class="no-file">-</span>
          </template>
        </el-table-column>
        
        <!-- 研发文件列 -->
        <el-table-column label="研发文件" width="140" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.children && scope.row.children.length == 0" 
                 class="rd-file-hover-container">
              
              <!-- 有文件时 -->
              <div v-if="scope.row.rdFileId" class="rd-file-display">
                <div class="rd-file-content">
                  <el-tooltip :content="scope.row.rdFileName" placement="top">
                    <span class="rd-file-name" @click="handleDownloadRdFile(scope.row.rdFileId)">
                      {{ scope.row.rdFileName }}
                    </span>
                  </el-tooltip>
                </div>
                
                <!-- 悬停时显示的操作按钮 -->
                <div class="rd-file-hover-actions">
                  <el-button-group size="mini">
                    <el-tooltip content="下载" placement="top">
                      <el-button 
                        type="text" 
                        icon="el-icon-download"
                        @click="handleDownloadRdFile(scope.row.rdFileId)">
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="替换" placement="top">
                      <el-button 
                        type="text" 
                        icon="el-icon-refresh"
                        v-hasPermi="['files:rd:upload']"
                        @click="handleUploadRdFile(scope.row)">
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="历史" placement="top">
                      <el-button 
                        type="text" 
                        icon="el-icon-time"
                        v-hasPermi="['files:rd:history']"
                        @click="handleRdFileHistory(scope.row)">
                      </el-button>
                    </el-tooltip>
                  </el-button-group>
                </div>
              </div>
              
              <!-- 无文件时 -->
              <div v-else class="rd-file-empty">
                <span class="no-file-text">-</span>
                <div class="rd-file-empty-actions">
                  <el-tooltip content="上传研发文件" placement="top">
                    <el-button
                      v-hasPermi="['files:rd:upload']"
                      size="mini"
                      type="primary"
                      icon="el-icon-plus"
                      @click="handleUploadRdFile(scope.row)">
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
            </div>
            <span v-else class="no-file">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="productionProperty" align="center" label="生产属性" width="100"></el-table-column>
        <el-table-column prop="category" align="center" label="类别" width="50"></el-table-column>
        <el-table-column prop="unit" align="center" label="单位" width="50"></el-table-column>
        <el-table-column prop="quantity" align="center" label="数量" width="50"></el-table-column>
        <el-table-column prop="remark" align="center" label="备注" width="150"></el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.children && scope.row.children.length == 0">
              <el-tooltip content="上传图纸" placement="top">
                <el-button
                  v-hasPermi="['files:upload']"
                  size="large"
                  type="text"
                  icon="el-icon-upload"
                  @click="handleUploadDrawing(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="变更历史" placement="top" v-if="hasAnyDrawingFile(scope.row)">
                <el-button
                  size="large"
                  type="text"
                  icon="el-icon-time"
                  v-hasPermi="['files:changeHistory']"
                  @click="handleViewHistory(scope.row)"></el-button>
              </el-tooltip>
            </div>
            <span v-else>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-else style="padding: 20px; text-align: center;">
      <el-empty description="无BOM数据或非BOM文件" :image-size="80"></el-empty>
    </div>
    
    <!-- BOM图纸变更历史对话框 -->
    <bom-drawing-history-dialog
      ref="bomDrawingHistoryDialog"
      @download-file="handleDownloadFile"
    />
    
    <!-- 图纸编号修改对话框 -->
    <drawing-number-edit-dialog
      :visible.sync="drawingNumberEdit.open"
      :bomItem="drawingNumberEdit.bomItem"
      @success="handleDrawingNumberEditSuccess"
    />
  </div>
</template>

<script>
import BomDrawingHistoryDialog from './BomDrawingHistoryDialog'
import DrawingNumberEditDialog from './DrawingNumberEditDialog'

export default {
  name: 'BomItemsTable',
  components: {
    BomDrawingHistoryDialog,
    DrawingNumberEditDialog
  },
  props: {
    fileId: {
      type: Number,
      required: true
    },
    modelCode: {
      type: String,
      default: ''
    },
    bomItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 图纸编号修改参数
      drawingNumberEdit: {
        open: false,
        bomItem: {}
      }
    }
  },
  methods: {
    // 上传图纸按钮操作
    handleUploadDrawing(row) {
      this.$emit('upload-drawing', row, this.modelCode);
    },
    
    // 修改图纸编号按钮操作
    handleEditDrawingNumber(row) {
      this.drawingNumberEdit.open = true;
      this.drawingNumberEdit.bomItem = row;
    },
    
    // 图纸编号修改成功回调
    handleDrawingNumberEditSuccess(data) {
      // 更新本地数据中的图纸编号
      const bomItem = this.drawingNumberEdit.bomItem;
      if (bomItem && bomItem.materialCode === data.materialCode) {
        bomItem.drawingNo = data.newDrawingNo;
      }
      
      // 通知父组件刷新数据
      this.$emit('drawing-number-updated', data);
      
      // 关闭弹窗
      this.drawingNumberEdit.open = false;
      this.drawingNumberEdit.bomItem = {};
    },
    
    // 下载图纸操作（向后兼容）
    handleDownloadDrawing(drawingFileId) {
      if (!drawingFileId) return;
      
      // 使用文件下载API
      this.$emit('download-drawing', drawingFileId);
    },
    
    // 下载文件操作（新的多文件支持）
    handleDownloadFile(fileId) {
      if (!fileId) return;
      
      // 使用已有的下载API，它包含权限检查
      this.$emit('download-drawing', fileId);
    },
    
    // 查看变更历史操作
    handleViewHistory(row) {
      if (!row.id) return;
      console.log('BomItemsTable - handleViewHistory 被调用，bomItemId:', row.id);
      // 使用新的历史对话框
      this.$refs.bomDrawingHistoryDialog.show(row);
    },
    
    // 检查是否有任何类型的图纸文件
    hasAnyDrawingFile(row) {
      return !!(
        row.drawingFileId ||     // 向后兼容旧字段
        row.pdfFileId ||         // PDF文件
        row.drawing2dFileId ||   // 2D图纸文件
        row.drawing3dFileId ||   // 3D图纸文件
        row.compressedFileId ||  // 压缩包文件
        row.rdFileId             // 研发文件
      );
    },
    
    // ========================= 研发文件相关方法 =========================
    
    // 下载研发文件
    handleDownloadRdFile(fileId) {
      if (!fileId) return;
      console.log('BomItemsTable - 下载研发文件，fileId:', fileId);
      this.$emit('download-rd-file', fileId);
    },
    
    // 上传研发文件
    handleUploadRdFile(row) {
      console.log('BomItemsTable - 上传研发文件，row:', row);
      this.$emit('upload-rd-file', row);
    },
    
    // 查看研发文件历史
    handleRdFileHistory(row) {
      if (!row.id) return;
      console.log('BomItemsTable - 查看研发文件历史，bomItemId:', row.id);
      this.$emit('view-rd-file-history', row);
    }
  }
}
</script>

<style scoped>
.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.drawing-no-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.edit-drawing-no-btn {
  padding: 2px 4px;
  margin-left: 5px;
  opacity: 0.6;
  transition: opacity 0.3s;
}

.edit-drawing-no-btn:hover {
  opacity: 1;
  color: #409eff;
}

.el-table .el-button--text {
  padding: 0;
}

.clickable-drawing-no {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
}

.clickable-file-name {
  color: #409EFF;
  text-decoration: underline;
  cursor: pointer;
  font-size: 12px;
  max-width: 75px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

.clickable-file-name:hover {
  color: #66b1ff;
}

.no-file {
  color: #c0c4cc;
  font-size: 12px;
}

/* ========================= 研发文件样式 ========================= */
.rd-file-hover-container {
  position: relative;
  min-height: 32px;
  width: 100%;
}

.rd-file-display {
  position: relative;
  width: 100%;
  
  .rd-file-content {
    .rd-file-name {
      color: #409EFF;
      cursor: pointer;
      font-size: 12px;
      max-width: 120px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.2;
      
      &:hover {
        text-decoration: underline;
        color: #66b1ff;
      }
    }
  }
  
  .rd-file-hover-actions {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    border-radius: 4px;
    
    .el-button-group .el-button {
      padding: 4px 6px;
      margin: 0 1px;
      font-size: 12px;
      
      &:hover {
        color: #409EFF;
        background-color: #ecf5ff;
      }
    }
  }
  
  &:hover .rd-file-hover-actions {
    opacity: 1;
  }
}

.rd-file-empty {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 32px;
  
  .no-file-text {
    color: #c0c4cc;
    font-size: 12px;
  }
  
  .rd-file-empty-actions {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    
    .el-button {
      padding: 3px 6px;
      font-size: 11px;
      min-height: auto;
    }
  }
  
  &:hover .rd-file-empty-actions {
    opacity: 1;
    
    .no-file-text {
      opacity: 0.3;
    }
  }
}
</style>
