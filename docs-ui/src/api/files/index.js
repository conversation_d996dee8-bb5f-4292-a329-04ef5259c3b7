import request from '@/utils/request'

// 查询文件列表
export function listFiles(query) {
  return request({
    url: '/files/list',
    method: 'get',
    params: query
  })
}

// 查询机型下拉列表
export function listProductModels() {
  return request({
    url: '/files/model/list',
    method: 'get'
  })
}

// 新增机型
export function addProductModel(data) {
  return request({
    url: '/files/model',
    method: 'post',
    data: data
  })
}

// 上传文件
export function uploadFile(data) {
  return request({
    url: '/files/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 获取文件变更历史
export function getFileChanges(query) {
  return request({
    url: '/files/version/history',
    method: 'get',
    params: query
  })
}

// 变更文件版本
export function changeFileVersion(data) {
  return request({
    url: '/files/version/change',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 下载文件
export function downloadFile(fileId) {
  return request({
    url: '/files/download/' + fileId,
    method: 'get',
    responseType: 'blob'
  })
}

// 下载文件（包含响应头）
export function downloadFileWithHeaders(fileId) {
  return request({
    url: '/files/download/' + fileId,
    method: 'get',
    responseType: 'blob',
    // 添加特殊标记，让响应拦截器返回完整响应
    headers: {
      'X-Return-Full-Response': 'true'
    }
  })
}

// 删除文件记录
export function deleteFile(fileId) {
  return request({
    url: '/files/delete/' + fileId,
    method: 'delete'
  })
}

// 上传BOM项图纸（单文件）
export function uploadDrawing(data) {
  return request({
    url: '/files/bom/drawing/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 上传BOM项图纸（多文件）
export function uploadMultipleDrawings(data) {
  return request({
    url: '/files/bom/drawing/upload/multiple',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 获取BOM物料图纸变更历史（旧版本，已废弃）
export function getBomItemDrawingChanges(bomItemId) {
  return request({
    url: `/files/bom/drawing/history/${bomItemId}`,
    method: 'get'
  })
}

// 获取BOM物料图纸变更历史（新版本）
export function getBomItemDrawingChangesV2(bomItemId, params = {}) {
  return request({
    url: `/files/bom/drawing/history/v2/${bomItemId}`,
    method: 'get',
    params: params
  })
}

// 获取文件详情
export function getFileDetail(fileId) {
  return request({
    url: `/files/${fileId}`,
    method: 'get'
  })
}

// 修改物料图纸编号
export function updateMaterialDrawingNumber(data) {
  return request({
    url: '/files/bom/drawing/update-number',
    method: 'post',
    data: data
  })
}

// 获取物料已存在的文件类型
export function getMaterialExistingFileTypes(materialCode) {
  return request({
    url: `/files/bom/drawing/existing-types/${materialCode}`,
    method: 'get'
  })
}

// ========================= 研发文件相关API =========================

// 上传研发文件
export function uploadRdFile(data) {
  return request({
    url: '/files/bom/uploadRdFile',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 获取研发文件变更历史
export function getRdFileHistory(bomItemId) {
  return request({
    url: `/files/bom/rdFileHistory/${bomItemId}`,
    method: 'get'
  })
}
