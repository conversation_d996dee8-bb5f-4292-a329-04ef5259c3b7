# BOM研发文件功能实现偏差记录

## 概述

本文档记录了实际实现与原始设计文档 `bom-rd-file-feature.md` 之间的偏差。这些偏差在开发过程中产生，需要进行统一和规范化。

## 重要偏差分析

### 1. 📋 缺失的下载接口

**文档描述**: 研发文件下载使用统一的 `/files/download/{fileId}` 接口
**实际实现**: ❌ **BomItemController中没有实现研发文件下载接口**

**当前状态**:
- 只实现了 `uploadRdFile`、`getRdFileHistory`、`getRdFileInfo` 接口
- **缺少统一的文件下载集成**，研发文件无法通过统一接口下载

**影响**: 
- 用户无法下载研发文件
- 研发文件权限控制无法生效
- 审批流程无法触发

### 2. 🔧 文件类型配置不一致  

**文档描述**: 研发文件使用独立的文件类型 `RD_FILE`
**实际实现**: ⚠️ **文件类型配置混乱**

**发现的问题**:
1. **数据库层面**: 已正确添加 `RD_FILE` 到 files 表的 file_type 枚举
2. **代码层面**: 在 `BomItemDrawingStrategy.uploadFileToSystem()` 中使用了 `FilesFileType.BOM_DRAWING` 而不是 `RD_FILE`
3. **权限识别**: `FileController.generateFileTypeMessage()` 中通过 `subType = "RD_FILE"` 识别研发文件，而不是通过 file_type

**正确的实现应该是**:
```java
// 研发文件应该使用独立的文件类型
fileInfo.setFileType(FilesFileType.RD_FILE)  // 而不是 BOM_DRAWING
fileInfo.setSubType(null)  // 或者特定的子类型
```

### 3. 🚫 删除接口的矛盾

**文档描述**: "移除独立下载/删除接口：不再提供独立的研发文件下载和删除接口"
**实际实现**: ❌ **实现中包含了删除相关代码**

**发现的矛盾**:
- `BomItemServiceImpl` 中包含 `deleteRdFile` 方法（但Controller中已移除）
- `BomItemDrawingStrategy` 中实现了 `deleteBomItemRdFile` 方法
- 文档明确说明不提供删除接口，但代码实现了删除功能

### 4. ✅ 权限配置文件已实现

**文档描述**: 需要创建独立的 `rd-file-permissions.yml` 配置文件
**实际实现**: ✅ **权限配置文件已正确实现**

**已实现的文件**:
```
/docs-files/src/main/resources/config/rd-file-permissions.yml
```

**状态**: 权限配置文件已正确创建并包含完整的配置项

### 5. 🔄 下载流程设计缺陷

**文档描述**: 通过 `rdFileInfo` 获取 fileId，然后调用统一下载接口
**实际实现**: ❌ **流程设计不合理**

**问题分析**:
1. 用户需要先调用 `/files/bom/rdFileInfo/{bomItemId}` 获取 fileId
2. 然后再调用 `/files/download/{fileId}` 下载文件
3. 这种设计增加了前端调用复杂度，且与其他文件的直接下载方式不一致

**建议的改进**:
- 提供直接的研发文件下载接口: `/files/bom/downloadRdFile/{bomItemId}`
- 在该接口内部获取 fileId 并调用统一下载服务

### 6. 📁 文件存储路径不统一

**实际实现发现的问题**:
```java
// 当前实现
String targetDir = FileConstants.BASE_UPLOAD_PATH + "/rd_files/" + materialCode;

// 应该与其他文件保持一致的路径结构
String targetDir = FileConstants.BASE_UPLOAD_PATH + "/drawing/" + materialCode + "/rd_file";
```

## 需要修复的关键问题

### 🔥 优先级1: 核心功能缺失

1. **实现研发文件下载接口**
   - 在 BomItemController 中添加 downloadRdFile 接口
   - 集成统一的文件下载和权限控制
   - 确保审批流程正常工作

2. **修复文件类型配置**
   - 使用正确的 `FilesFileType.RD_FILE`
   - 统一文件类型识别逻辑

### 🔥 优先级2: 配置和权限

3. **统一文件存储路径**
   - 使用一致的路径命名规范

### 🔥 优先级3: 代码清理

5. **清理删除相关代码**
   - 如果确定不需要删除功能，移除相关代码
   - 或者在文档中说明保留删除功能的原因

6. **改进下载流程**
   - 简化用户调用流程
   - 提供更直观的API设计

## 建议的实现方案

### 方案1: 最小化修复（推荐）

1. 在 BomItemController 中添加研发文件下载接口：
```java
@PreAuthorize("@ss.hasPermi('files:rd:download')")
@GetMapping("/downloadRdFile/{bomItemId}")
public void downloadRdFile(@PathVariable Integer bomItemId, HttpServletResponse response) {
    // 获取研发文件信息
    // 调用统一下载服务
}
```

2. 修复文件类型配置，使用正确的 `RD_FILE` 枚举值

3. 创建基本的权限配置文件

### 方案2: 完整重构

1. 重新设计研发文件的文件类型和存储逻辑
2. 实现完整的权限配置系统
3. 统一所有文件操作的API设计

## 影响评估

### 用户影响
- ❌ **研发文件功能无法正常使用**（无法下载）
- ⚠️ 权限控制可能不生效
- ⚠️ 审批流程无法触发

### 开发影响
- 需要额外的开发时间修复关键缺陷
- 前端集成可能需要调整
- 测试用例需要更新

### 部署影响
- 需要数据库迁移脚本的小幅调整
- 配置文件需要添加

## 建议的修复计划

### 第一阶段（立即）: 修复核心功能
- [ ] 实现研发文件下载接口
- [ ] 修复文件类型配置
- [ ] 基本功能测试

### 第二阶段（短期）: 完善配置和权限
- [ ] 统一文件路径
- [ ] 集成审批流程

### 第三阶段（中期）: 优化和清理
- [ ] 代码清理和重构
- [ ] 性能优化
- [ ] 完整测试

---

## 总结

当前实现存在一些关键的功能缺陷，主要是**研发文件下载功能完全缺失**和**文件类型配置混乱**。这些问题需要优先解决，否则研发文件功能无法正常使用。建议采用最小化修复方案，先确保基本功能可用，再逐步完善其他特性。