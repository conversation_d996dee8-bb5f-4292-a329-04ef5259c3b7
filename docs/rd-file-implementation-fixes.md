# BOM研发文件功能修复记录

## 概述

本文档记录了针对 `rd-file-implementation-deviations.md` 中发现的问题所进行的修复。

## 已完成的修复

### ✅ 1. 文件类型配置修复

**问题**: 研发文件使用了错误的 `FilesFileType.BOM_DRAWING` 类型
**修复**: 
- 在 `BomItemDrawingStrategy.uploadFileToSystem()` 中改为使用 `FilesFileType.RD_FILE`
- 设置 `subType` 为 `null`，因为研发文件不需要子类型

**修改文件**:
- `docs-files/src/main/java/com/docs/files/service/strategy/BomItemDrawingStrategy.java`

```java
// 修复前
fileInfo.setFileType(FilesFileType.BOM_DRAWING)
        .setSubType(fileType)

// 修复后  
fileInfo.setFileType(FilesFileType.RD_FILE)
        .setSubType(null)  // 研发文件不需要子类型
```

### ✅ 2. 文件存储路径修复

**问题**: 文件路径使用 `rd_files`
**修复**: 改为使用 `rd` 目录

**修改**:
```java
// 修复前
String targetDir = FileConstants.BASE_UPLOAD_PATH + "/rd_files/" + materialCode;

// 修复后
String targetDir = FileConstants.BASE_UPLOAD_PATH + "/rd/" + materialCode;
```

### ✅ 3. 移除不需要的接口和方法

**移除的接口**:
- ❌ `GET /files/bom/rdFileInfo/{bomItemId}` - Controller接口已删除
- ❌ `BomItemService.getRdFileInfo()` - Service接口已删除  
- ❌ `BomItemServiceImpl.getRdFileInfo()` - Service实现已删除
- ❌ `BomItemDrawingStrategy.getBomItemRdFileInfo()` - Strategy方法已删除

**移除的删除相关代码**:
- ❌ `BomItemDrawingStrategy.deleteBomItemRdFile()` - 删除方法已移除
- ❌ `BomItemDrawingStrategy.downloadFileFromSystem()` - 私有下载方法已移除（使用统一下载）
- ❌ `BomItemDrawingStrategy.redirectToApprovalRequest()` - 重定向方法已移除

### ✅ 4. 文件类型识别逻辑修复

**问题**: FileController 中通过 `BOM_DRAWING` + `subType="RD_FILE"` 识别研发文件
**修复**: 直接通过 `RD_FILE` 文件类型识别

**修改文件**:
- `docs-admin/src/main/java/com/docs/web/controller/files/FileController.java`

```java
// 修复后的逻辑
switch (fileInfo.getFileType()) {
    case RD_FILE:
        return "研发文件";
    case BOM_DRAWING:
        return getDrawingTypeMessage(fileInfo.getSubType());
    // ... 其他类型
}
```

## 当前API接口

### 保留的研发文件接口

1. **上传研发文件**
   ```
   POST /files/bom/uploadRdFile
   - file: MultipartFile
   - bomItemId: Integer  
   - reason: String (可选)
   ```

2. **下载研发文件** (统一接口)
   ```
   GET /files/download/{fileId}
   - 通过统一的文件下载接口
   - 自动识别 RD_FILE 类型并应用相应权限
   ```

3. **获取研发文件变更历史**
   ```
   GET /files/bom/rdFileHistory/{bomItemId}
   ```

### 移除的接口

- ❌ `GET /files/bom/rdFileInfo/{bomItemId}` - 已移除，文件信息通过统一接口获取

## 权限控制

研发文件权限保持不变：
- `files:rd:upload` - 上传权限
- `files:rd:download` - 下载权限  
- `files:rd:history` - 历史查看权限

## 文件类型结构

```
研发文件在数据库中的存储：
- file_type: RD_FILE (枚举值)
- sub_type: null (研发文件不需要子类型)
- 存储路径: {BASE_UPLOAD_PATH}/rd/{materialCode}/
```

## 数据库兼容性

由于文件类型从 `BOM_DRAWING` 改为 `RD_FILE`，可能需要数据迁移脚本来更新现有数据：

```sql
-- 如果已经有使用旧类型的研发文件数据，需要执行以下迁移
UPDATE files 
SET file_type = 'RD_FILE', sub_type = NULL 
WHERE file_type = 'BOM_DRAWING' AND sub_type = 'RD_FILE';
```

## 前端集成更新

由于移除了 `rdFileInfo` 接口，前端需要调整：

### 移除的调用
```javascript
// ❌ 不再可用
fetch(`/files/bom/rdFileInfo/${bomItemId}`)
```

### 替代方案
```javascript
// ✅ 通过BOM列表或其他方式获取研发文件的fileId
// 然后使用统一下载接口
window.open(`/files/download/${fileId}`);
```

## 验证清单

### 功能验证
- [ ] 研发文件上传功能正常
- [ ] 文件使用正确的 RD_FILE 类型存储
- [ ] 文件存储在正确的 `/rd/` 目录下
- [ ] 统一下载接口能正确识别研发文件类型
- [ ] 权限控制正常工作
- [ ] 变更历史记录正常

### 代码验证  
- [x] 移除了所有删除相关代码
- [x] 移除了 rdFileInfo 相关接口和方法
- [x] 修复了文件类型配置
- [x] 修复了文件路径
- [x] 更新了文件类型识别逻辑

### 编译验证
- [ ] 项目能正常编译
- [ ] 没有未使用的导入
- [ ] 没有未实现的方法

## 后续工作

1. **数据迁移**: 如果系统中已有研发文件数据，需要执行数据迁移脚本
2. **前端更新**: 前端代码需要移除对 `rdFileInfo` 接口的调用
3. **测试**: 完整的功能测试和集成测试
4. **文档更新**: 更新用户文档和API文档

---

## 总结

通过这次修复，研发文件功能已经符合了设计要求：
- ✅ 使用正确的 `RD_FILE` 文件类型
- ✅ 统一的下载接口和权限控制
- ✅ 简化的API结构（移除了不必要的接口）
- ✅ 独立的文件存储路径
- ✅ 清理了删除相关的冗余代码