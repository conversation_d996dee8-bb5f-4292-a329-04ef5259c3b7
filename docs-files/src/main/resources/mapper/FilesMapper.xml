<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.docs.filesEntity.mapper.FilesMapper">
  <resultMap id="BaseResultMap" type="com.docs.common.core.domain.entity.FilesEntity">
    <!--@mbg.generated-->
    <!--@Table filesEntity-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="product_model_id" jdbcType="INTEGER" property="productModelId" />
    <result column="file_type" jdbcType="OTHER" property="fileType" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
    <result column="drawing_no" jdbcType="VARCHAR" property="drawingNo" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="model_code" property="modelCode"/>
    <result column="material_code" property="materialCode"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, product_model_id, file_type, sub_type,drawing_no, version, `status`, file_path, created_at,
    updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from filesEntity
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from filesEntity
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.FilesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into filesEntity (product_model_id, file_type, sub_type, drawing_no,
      version, `status`, file_path)
    values (#{productModelId,jdbcType=INTEGER}, #{fileType,jdbcType=OTHER}, #{subType,jdbcType=VARCHAR}, 
      #{drawingNo,jdbcType=VARCHAR},#{version,jdbcType=VARCHAR}, #{status,jdbcType=OTHER}, #{filePath,jdbcType=VARCHAR} )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.FilesEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into filesEntity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productModelId != null">
        product_model_id,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="subType != null and subType != ''">
        sub_type,
      </if>
      <if test="drawingNo != null and drawingNo != ''">
        drawing_no,
      </if>
      <if test="version != null and version != ''">
        version,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="filePath != null and filePath != ''">
        file_path,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productModelId != null">
        #{productModelId,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=OTHER},
      </if>
      <if test="subType != null and subType != ''">
        #{subType,jdbcType=VARCHAR},
      </if>
      <if test="drawingNo != null and drawingNo != ''">
        #{drawingNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null and version != ''">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=OTHER},
      </if>
      <if test="filePath != null and filePath != ''">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.docs.common.core.domain.entity.FilesEntity">
    <!--@mbg.generated-->
    update filesEntity
    <set>
      <if test="productModelId != null">
        product_model_id = #{productModelId,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=OTHER},
      </if>
      <if test="subType != null and subType != ''">
        sub_type = #{subType,jdbcType=VARCHAR},
      </if>
      <if test="drawingNo != null and drawingNo != ''">
        drawing_no = #{drawingNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null and version != ''">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=OTHER},
      </if>
      <if test="filePath != null and filePath != ''">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.docs.common.core.domain.entity.FilesEntity">
    <!--@mbg.generated-->
    update filesEntity
    set product_model_id = #{productModelId,jdbcType=INTEGER},
      file_type = #{fileType,jdbcType=OTHER},
      sub_type = #{subType,jdbcType=VARCHAR},
      drawing_no = #{drawingNo,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=OTHER},
      file_path = #{filePath,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update filesEntity
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="product_model_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.productModelId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="file_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fileType,jdbcType=OTHER}
        </foreach>
      </trim>
      <trim prefix="sub_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.subType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="drawing_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.drawingNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.version,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=OTHER}
        </foreach>
      </trim>
      <trim prefix="file_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.filePath,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="created_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createdAt,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updated_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updatedAt,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update filesEntity
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="product_model_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productModelId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.productModelId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="file_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fileType != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.fileType,jdbcType=OTHER}
          </if>
        </foreach>
      </trim>
      <trim prefix="sub_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.subType != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.subType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="drawing_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.drawingNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.version,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=OTHER}
          </if>
        </foreach>
      </trim>
      <trim prefix="file_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.filePath != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.filePath,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdAt != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createdAt,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedAt != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updatedAt,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into filesEntity
    (product_model_id, file_type, sub_type,drawing_no, version, `status`, file_path)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.productModelId,jdbcType=INTEGER}, #{item.fileType,jdbcType=OTHER}, #{item.subType,jdbcType=VARCHAR}, 
       #{item.drawingNo,jdbcType=VARCHAR}, #{item.version,jdbcType=VARCHAR}, #{item.status,jdbcType=OTHER}, #{item.filePath,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <select id="selectFilesList" parameterType="FilesEntity" resultMap="BaseResultMap">
    select f.*, pm.model_code
    from filesEntity f
    left join product_models pm on f.product_model_id = pm.id
    left join material_drawing_relations mdr on f.drawing_no = mdr.drawing_no
    <where>
        <!-- 排除BOM关联图纸文件，这些文件只应在BOM上下文中可见 -->
        and f.file_type != 'BOM_DRAWING'
        <!-- 排除研发文件，这些文件只应在BOM研发文件管理中可见 -->
        and f.file_type != 'RD_FILE'
        <if test="modelIds != null and modelIds.size() > 0">
            and f.product_model_id in
            <foreach collection="modelIds" item="modelId" open="(" separator="," close=")">
                #{modelId}
            </foreach>
        </if>
        <if test="fileType != null and fileType != ''">
            and f.file_type = #{fileType}
        </if>
        <if test="status != null and status != ''">
            and f.status = #{status}
        </if>
        <if test="drawingNo != null and drawingNo != ''">
          and f.drawing_no = #{drawingNo,jdbcType=VARCHAR}
        </if>
      <if test="materialCode != null and materialCode != ''">
<!--        and f.m-->
      </if>
    </where>
    order by f.created_at desc
  </select>

  <select id="selectByVersion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from filesEntity
    where product_model_id = #{productModelId}
    and file_type = #{fileType}
    and sub_type = #{subType}
    and version = #{version}
    and status = '使用'
</select> 
</mapper>