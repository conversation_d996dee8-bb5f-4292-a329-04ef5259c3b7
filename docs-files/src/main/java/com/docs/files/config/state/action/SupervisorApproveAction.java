package com.docs.files.config.state.action;

import static com.docs.common.jooq.generated.Tables.APPROVAL_ACTION_RECORDS;
import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;

import org.jooq.DSLContext;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

import com.docs.common.constant.UserConstants;
import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 主管批准动作
 * 当状态从 PENDING_SUPERVISOR_APPROVAL 转换到 PENDING_MANAGER_APPROVAL 时执行
 */
@Component
@Slf4j
public class SupervisorApproveAction extends BaseApprovalAction {

    public SupervisorApproveAction(DSLContext dsl) {
        super(dsl);
    }

    @Override
    public void execute(StateContext<ApprovalStatus, ApprovalEvent> context) {
        Long approvalRequestId = getApprovalRequestId(context);
        Long actorId = getActorId(context);
        String comment = getComment(context);

        logger.info("执行主管批准动作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);

        // 获取当前请求记录，确认状态
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();

        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalStateException("审批请求不存在: " + approvalRequestId);
        }

        // 确认当前状态是 PENDING_SUPERVISOR_APPROVAL
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        if (currentStatus != ApprovalStatus.PENDING_SUPERVISOR_APPROVAL) {
            logger.error("当前状态不符: {}, 期望状态: {}",
                       currentStatus, ApprovalStatus.PENDING_SUPERVISOR_APPROVAL);
            throw new IllegalStateException("当前状态不符: " + currentStatus);
        }

        // 确认操作人是指定的主管
        if (!actorId.equals(requestRecord.getSupervisorId())) {
            logger.error("操作人 {} 不是指定的主管 {}", actorId, requestRecord.getSupervisorId());
            throw new IllegalStateException("只有指定的主管可以批准此请求");
        }

        // 确定经理ID (这里是简化实现，实际应该根据业务规则确定)
        Long managerId = determineManagerId(requestRecord.getRequesterId());

        // 更新审批请求状态
        dsl.update(DOWNLOAD_APPROVAL_REQUESTS)
           .set(DOWNLOAD_APPROVAL_REQUESTS.STATUS, ApprovalStatus.PENDING_MANAGER_APPROVAL.name())
           .set(DOWNLOAD_APPROVAL_REQUESTS.MANAGER_ID, managerId)
           .set(DOWNLOAD_APPROVAL_REQUESTS.UPDATE_TIME, LocalDateTime.now())
           .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
           .execute();

        // 记录操作
        createActionRecord(
            approvalRequestId,
            actorId,
            ApprovalActionType.SUPERVISOR_APPROVE,
            comment,
            ApprovalStatus.PENDING_SUPERVISOR_APPROVAL,
            ApprovalStatus.PENDING_MANAGER_APPROVAL
        );

        log.info("主管批准动作完成。请求 {} 状态已更新为 {} 并分配给经理 {}",
                  approvalRequestId, ApprovalStatus.PENDING_MANAGER_APPROVAL, managerId);
    }

    /**
     * 确定经理ID的方法
     * 经理不按部门查找，而是全局查找第一个具有经理岗位的用户
     *
     * @param requesterId 申请人ID（此参数在经理查找中不再使用，但保留以保持接口兼容性）
     * @return 经理ID
     */
    private Long determineManagerId(Long requesterId) {
        log.info("全局查找经理用户");

        try {
            // 直接查询所有具有经理岗位的用户，不限制部门
            String sql = "SELECT u.user_id FROM sys_user u " +
                         "JOIN sys_user_post up ON u.user_id = up.user_id " +
                         "JOIN sys_post p ON up.post_id = p.post_id " +
                         "WHERE p.post_code = ? AND u.status = '0' " +
                         "LIMIT 1";

            Long managerId = dsl.resultQuery(sql, UserConstants.POST_MANAGER)
                    .fetchOneInto(Long.class);

            if (managerId != null) {
                log.info("找到经理用户，ID: {}", managerId);
                return managerId;
            } else {
                log.error("系统中未找到任何经理用户");
                throw new RuntimeException("系统中未找到任何经理用户，请联系系统管理员");
            }
        } catch (Exception e) {
            log.error("查找经理用户时发生异常", e);
            throw new RuntimeException("查找经理用户失败: " + e.getMessage(), e);
        }
    }
}
