package com.docs.files.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.BomItem;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.daos.BomItemsDao;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.BomItems;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import com.docs.common.utils.bean.BeanUtils;
import com.docs.files.listener.BomExcelListener;
import com.docs.files.service.BomItemService;
import com.docs.files.service.MaterialDrawingMapService;
import com.docs.files.service.strategy.BomItemDrawingStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.docs.common.jooq.generated.Tables.BOM_ITEMS;

/**
 * BOM数据服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BomItemServiceImpl  implements BomItemService {

    private final BomItemDrawingStrategy bomItemDrawingStrategy;
    private final BomItemsDao bomItemsDao;
    private final FilesDao filesDao;
    private final MaterialDrawingMapService materialDrawingMapService;

    /**
     * 根据文件ID查询BOM数据，转换为VO
     *
     * @param fileId 文件ID
     * @return BomItemsVO列表
     */
    @Override
    public List<BomItemsVO> selectByFileId(Integer fileId) {
        // 使用包含图纸信息的批量查询方法
        return selectByFileIds(Collections.singleton(fileId));
    }

    /**
     * 批量根据文件ID查询BOM数据
     *
     * @param fileIds 文件ID集合
     * @return BomItemsVO列表，包含图纸编号和图纸ID
     */
    @Override
    public List<BomItemsVO> selectByFileIds(Set<Integer> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return Collections.emptyList();
        }
        log.debug("Batch fetching BOM items for file IDs: {}", fileIds);

        // 使用 jOOQ 的 in 条件进行批量查询
        List<BomItems> items = bomItemsDao.ctx()
                .selectFrom(BOM_ITEMS)
                .where(BOM_ITEMS.FILE_ID.in(fileIds))
                .orderBy(BOM_ITEMS.FILE_ID, BOM_ITEMS.ID) // 按文件ID和BOM项ID排序，保证顺序
                .fetchInto(BomItems.class);
        
        if (items.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 提取所有物料编码
        List<String> materialCodes = items.stream()
                .map(BomItems::getMaterialCode)
                .filter(code -> code != null && !code.isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        // 批量查询物料-图纸映射关系
        Map<String, MaterialDrawingMap> materialDrawingMaps = Collections.emptyMap();
        if (!materialCodes.isEmpty()) {
            materialDrawingMaps = materialDrawingMapService.findByMaterialCodes(materialCodes);
            log.debug("Found {} material-drawing mappings for {} material codes", materialDrawingMaps.size(), materialCodes.size());
        }
        
        // 转换为 BomItemsVO 并填充图纸信息
        Map<String, MaterialDrawingMap> finalMaterialDrawingMaps = materialDrawingMaps;
        List<BomItemsVO> bomItemsVOList = items.stream()
                .map(item -> convertToBomItemsVO(item, finalMaterialDrawingMaps))
                .collect(Collectors.toList());
                
        // 批量获取文件名并填充
        fillFileNames(bomItemsVOList);
        
        return bomItemsVOList;
    }

    /**
     * 将 BomItems POJO 转换为 BomItemsVO DTO
     */
    private BomItemsVO convertToBomItemsVO(BomItems item) {
        if (item == null) {
            return null;
        }
        BomItemsVO vo = new BomItemsVO();
        // 使用 BeanUtils 或手动映射字段
        BeanUtils.copyProperties(item, vo); // 假设 BeanUtils 可用
        return vo;
    }
    
    /**
     * 将 BomItems POJO 转换为 BomItemsVO DTO，并填充图纸信息
     * 
     * @param item BomItems 对象
     * @param materialDrawingMaps 物料编码到物料-图纸映射的映射
     * @return 填充了图纸信息的 BomItemsVO 对象
     */
    private BomItemsVO convertToBomItemsVO(BomItems item, Map<String, MaterialDrawingMap> materialDrawingMaps) {
        BomItemsVO vo = convertToBomItemsVO(item);
        if (vo == null) {
            return null;
        }
        
        // 填充图纸信息（包含多文件支持）
        String materialCode = item.getMaterialCode();
        if (materialCode != null && !materialCode.isEmpty() && materialDrawingMaps.containsKey(materialCode)) {
            MaterialDrawingMap mapping = materialDrawingMaps.get(materialCode);
            vo.setDrawingNo(mapping.getDrawingNo());
            
            // 向后兼容：如果没有多文件，使用原有字段
            vo.setDrawingFileId(mapping.getDrawingFileId());
            
            // 填充多文件字段
            vo.setPdfFileId(mapping.getPdfFileId());
            vo.setDrawing2dFileId(mapping.getDrawing_2dFileId());
            vo.setDrawing3dFileId(mapping.getDrawing_3dFileId());
            vo.setCompressedFileId(mapping.getCompressedFileId());
            
            log.debug("Filled multi-file drawing info for material code {}: drawingNo={}, pdf={}, 2d={}, 3d={}, compressed={}", 
                    materialCode, mapping.getDrawingNo(), mapping.getPdfFileId(), 
                    mapping.getDrawing_2dFileId(), mapping.getDrawing_3dFileId(), mapping.getCompressedFileId());
        }
        
        return vo;
    }
    
    /**
     * 批量获取文件名并填充到BomItemsVO中
     */
    private void fillFileNames(List<BomItemsVO> bomItemsVOList) {
        if (bomItemsVOList == null || bomItemsVOList.isEmpty()) {
            return;
        }
        
        // 收集所有文件ID
        Set<Integer> allFileIds = new HashSet<>();
        for (BomItemsVO vo : bomItemsVOList) {
            if (vo.getPdfFileId() != null) allFileIds.add(vo.getPdfFileId());
            if (vo.getDrawing2dFileId() != null) allFileIds.add(vo.getDrawing2dFileId());
            if (vo.getDrawing3dFileId() != null) allFileIds.add(vo.getDrawing3dFileId());
            if (vo.getCompressedFileId() != null) allFileIds.add(vo.getCompressedFileId());
        }
        
        if (allFileIds.isEmpty()) {
            return;
        }
        
        // 批量查询文件名
        Map<Integer, String> fileIdToNameMap = new HashMap<>();
        for (Integer fileId : allFileIds) {
            Files file = filesDao.findById(fileId);
            if (file != null) {
                fileIdToNameMap.put(fileId, file.getFileName());
            }
        }
        
        // 填充文件名到VO中
        for (BomItemsVO vo : bomItemsVOList) {
            if (vo.getPdfFileId() != null) {
                vo.setPdfFileName(fileIdToNameMap.get(vo.getPdfFileId()));
            }
            if (vo.getDrawing2dFileId() != null) {
                vo.setDrawing2dFileName(fileIdToNameMap.get(vo.getDrawing2dFileId()));
            }
            if (vo.getDrawing3dFileId() != null) {
                vo.setDrawing3dFileName(fileIdToNameMap.get(vo.getDrawing3dFileId()));
            }
            if (vo.getCompressedFileId() != null) {
                vo.setCompressedFileName(fileIdToNameMap.get(vo.getCompressedFileId()));
            }
        }
    }

    /**
     * 查询BOM数据列表
     *
     * @param bomItem
     */
    @Override
    public List<BomItems> selectBomItemList(BomItem bomItem) {
        return bomItemsDao.fetch(BOM_ITEMS.FILE_ID, bomItem.getFileId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int parseBomExcelAndSave(Files files, String filePath, String bomType) {
        try {
            // 1. 清除之前的数据
            bomItemsDao.deleteById(files.getId());

            // 2. 解析Excel文件
            List<BomItems> bomItems = parseExcelFile(filePath, files, bomType);

            // 3. 批量保存BOM数据
            if (!bomItems.isEmpty()) {
                bomItemsDao.insert(bomItems);
            }
            return bomItems.size();
        } catch (Exception e) {
            log.error("解析BOM Excel文件失败", e);
            throw new ServiceException("解析BOM Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 解析Excel文件
     */
    private List<BomItems> parseExcelFile(String filePath, Files files, String bomType) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new ServiceException("文件不存在: " + filePath);
        }

        // 创建监听器
        BomExcelListener listener = new BomExcelListener(files.getId(), bomType);

        // 使用EasyExcel读取Excel文件
        EasyExcel.read(file, listener)
                .sheet("组件BOM表 ")
                .headRowNumber(0)
                .doRead();

        Map<String, String> headers = listener.getHeaders();
        updatedHeaders(files, headers);

        return listener.getBomItems();
    }

    public void updatedHeaders(Files files, Map<String, String> headers) {
        files.setBomHeader(headers.get("bomHeader"));
        files.setBomSubheader(headers.get("bomSubheader"));
        filesDao.update(files);
    }

    @Override
    public void updateBomItems(List<BomItemsVO> bomItems) {
        List<BomItems> bomItemsList = BeanUtil.copyToList(bomItems, BomItems.class);
        bomItemsDao.update(bomItemsList);
    }

    @Override
    public void deleteBomitemsByFileId(Integer fileId) {
        bomItemsDao.ctx().delete(BOM_ITEMS).where(BOM_ITEMS.FILE_ID.eq(fileId)).execute();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadDrawing(MultipartFile file, Integer bomItemId, String drawingNo, String subType, String remark) {
        return bomItemDrawingStrategy.uploadBomItemDrawing(file, bomItemId, drawingNo, subType, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadMultipleDrawings(MultipartFile[] files, Integer bomItemId, String drawingNo, String[] fileTypes, String remark) {
        return bomItemDrawingStrategy.uploadBomItemDrawings(files, bomItemId, drawingNo, fileTypes, remark);
    }

    // ========================= 研发文件管理实现 =========================
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadRdFile(MultipartFile file, Integer bomItemId, String reason) {
        log.info("Service层上传研发文件: bomItemId={}, fileName={}, reason={}", 
                   bomItemId, file.getOriginalFilename(), reason);
        
        try {
            // 复用现有的图纸上传策略，但文件类型标记为 RD_FILE
            return bomItemDrawingStrategy.uploadBomItemRdFile(file, bomItemId, reason);
        } catch (Exception e) {
            log.error("Service层上传研发文件失败: bomItemId={}, fileName={}", 
                        bomItemId, file.getOriginalFilename(), e);
            throw new RuntimeException("上传研发文件失败: " + e.getMessage(), e);
        }
    }



    @Override
    public AjaxResult getRdFileChangeHistory(Integer bomItemId) {
        log.info("Service层查询研发文件变更历史: bomItemId={}", bomItemId);
        
        try {
            return bomItemDrawingStrategy.getBomItemFileChangeHistory(bomItemId, "RD_FILE");
        } catch (Exception e) {
            log.error("Service层查询研发文件变更历史失败: bomItemId={}", bomItemId, e);
            throw new RuntimeException("查询研发文件变更历史失败: " + e.getMessage(), e);
        }
    }

}