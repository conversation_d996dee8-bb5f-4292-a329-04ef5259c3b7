package com.docs.files.service.impl;

import com.docs.common.constant.FileConstants;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.FilesEntity;
import com.docs.common.core.domain.entity.SysRole;
import com.docs.common.core.domain.model.FileChangeRequest;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.core.domain.vo.FileListVO;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.daos.FileChangesDao;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.FileChanges;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.common.utils.SecurityUtils;
import com.docs.common.utils.TreeUtils;
import com.docs.common.utils.bean.BeanUtils;
import com.docs.files.service.*;
import com.docs.files.service.processing.FileProcessingService;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.VersionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.docs.common.jooq.generated.Tables.*;

//@Service("oldFilesService")
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class FilesServiceImpl implements FilesService {
    private final FilesDao filesDao;
    private final FileChangesDao fileChangesDao;

    private final ProductModelsService productModelsService;
    private final BomItemService bomItemService;
    private final FileProcessingService fileProcessingService;
    private final MaterialDrawingRelationsService materialDrawingRelationsService;
    private final MaterialDrawingMapService materialDrawingMapService;

    /**
     * 获取用户可访问的机型角色列表。
     */
    private Set<String> getUserModelCodes() {
        if (SecurityUtils.isAdmin()) {
            // 管理员返回所有机型
            return productModelsService.selectProductModelsList(new ProductModels()).stream()
                    .map(ProductModels::getModelCode).collect(Collectors.toSet());
        }

        // 普通用户返回有权限的机型
        return SecurityUtils.getLoginUser().getUser().getRoles().stream().map(SysRole::getRoleName)
                .filter(roleName -> roleName.startsWith(FileConstants.MODEL_ROLE_PREFIX))
                .map(roleName -> roleName.substring(FileConstants.MODEL_ROLE_PREFIX.length()))
                .collect(Collectors.toSet());
    }


    /**
     * 查询文件列表
     *
     * @param filesEntity 文件查询参数
     * @return 文件列表视图对象
     */
    @Override
    public List<FileListVO> selectFilesList(FilesEntity filesEntity) {
        DSLContext dsl = filesDao.ctx();
        SelectJoinStep<? extends Record> query = dsl
                .select(FILES.ID, FILES.FILE_NAME, FILES.PRODUCT_MODEL_ID, FILES.FILE_TYPE, FILES.SUB_TYPE, FILES.VERSION, FILES.STATUS,
                        FILES.FILE_PATH, FILES.CREATED_AT, FILES.UPDATED_AT, FILES.DRAWING_NO, FILES.BOM_HEADER,
                        FILES.BOM_SUBHEADER, PRODUCT_MODELS.MODEL_CODE)
                .from(PRODUCT_MODELS)
                .leftJoin(FILES).on(PRODUCT_MODELS.ID.eq(FILES.PRODUCT_MODEL_ID));

        SelectConditionStep<? extends Record> whereClause = query.where();

        // 排除BOM关联图纸文件，这些文件只应在BOM上下文中可见
        whereClause = whereClause.and(FILES.FILE_TYPE.ne(FilesFileType.BOM_DRAWING));

        // 排除研发文件，这些文件只应在BOM研发文件管理中可见
        whereClause = whereClause.and(FILES.FILE_TYPE.ne(FilesFileType.RD_FILE));

        // 应用权限过滤
        if (!SecurityUtils.isAdmin()) {
            Set<Integer> modelIds = getUserModelIds();
            if (modelIds.isEmpty()) {
                return new ArrayList<>(); // 无可访问的机型
            }
            whereClause = whereClause.and(PRODUCT_MODELS.ID.in(modelIds));
        }

        // 应用来自'files'实体的搜索条件
        if (filesEntity.getProductModelId() != null) {
            whereClause = whereClause.and(FILES.PRODUCT_MODEL_ID.eq(filesEntity.getProductModelId()));
        }
        if (filesEntity.getFileType() != null && !filesEntity.getFileType().isEmpty()) {
            try {
                whereClause = whereClause.and(FILES.FILE_TYPE.eq(FilesFileType.valueOf(filesEntity.getFileType())));
            } catch (IllegalArgumentException e) {
                log.warn("查询中提供了无效的文件类型: {}", filesEntity.getFileType());
                // 可选地返回空或抛出错误, 这里忽略无效类型
            }
        }
        if (filesEntity.getSubType() != null && !filesEntity.getSubType().isEmpty()) {
            whereClause = whereClause.and(FILES.SUB_TYPE.eq(filesEntity.getSubType()));
        }
        if (filesEntity.getStatus() != null && !filesEntity.getStatus().isEmpty()) {
            try {
                whereClause = whereClause.and(FILES.STATUS.eq(FilesStatus.valueOf(filesEntity.getStatus())));
            } catch (IllegalArgumentException e) {
                log.warn("查询中提供了无效的状态: {}", filesEntity.getStatus());
            }
        }
        if (filesEntity.getDrawingNo() != null && !filesEntity.getDrawingNo().isEmpty()) {
            whereClause = whereClause.and(FILES.DRAWING_NO.like("%" + filesEntity.getDrawingNo() + "%"));
        }

        // 注意: 物料编号过滤将在获取BOM项后进行
        // (物料编号不直接存储在FILES表中)

        List<FileListVO> resultList = whereClause.orderBy(FILES.CREATED_AT.desc()).fetchInto(FileListVO.class);

        // 查询所有有变更历史的文件ID
        Set<Integer> fileIdsWithChanges = dsl
                .selectDistinct(FILE_CHANGES.FILE_ID)
                .from(FILE_CHANGES)
                .fetch(FILE_CHANGES.FILE_ID)
                .stream()
                .collect(Collectors.toSet());

        // 处理物料编号过滤
        final String materialNo = filesEntity.getMaterialNo();
        final boolean hasMaterialNoFilter = StringUtils.isNotBlank(materialNo);

        for (FileListVO vo : resultList) {
            // 设置文件是否有变更历史
            vo.setHasChanges(fileIdsWithChanges.contains(vo.getId()));

            if (vo.getFileType() == FilesFileType.BOM) {
                List<BomItemsVO> flatBomItems = bomItemService.selectByFileId(vo.getId());

                if (hasMaterialNoFilter) {
                    // 使用物料编号过滤方法
                    vo.setBomItems(filterAndBuildBomTree(flatBomItems, materialNo));
                } else {
                    // 使用常规树构建
                    vo.setBomItems(buildBomTree(flatBomItems));
                }
            }
        }

        return resultList;
    }

    /**
     * 根据物料编号过滤BOM项并构建层级树。
     * 仅包含直接匹配项(按物料编号)、它们的祖先和它们的后代。
     * 不匹配的兄弟节点及其后代将被排除。
     *
     * @param flatList   BOM项的扁平列表
     * @param materialNo 用于过滤的物料编号
     * @return 匹配的BOM项的层级树
     */
    private List<BomItemsVO> filterAndBuildBomTree(List<BomItemsVO> flatList, String materialNo) {
        if (flatList == null || flatList.isEmpty()) {
            log.debug("BOM过滤: 扁平列表为空, 返回空树");
            return new ArrayList<>();
        }

        log.debug("BOM过滤: 按物料编号 '{}' 过滤 {} 个项目", materialNo, flatList.size());

        // 步骤 1: 标记需要包含的节点
        Set<Integer> nodeIdsToInclude = markNodesForInclusion(flatList, materialNo);

        // 如果未找到匹配项, 返回空列表
        if (nodeIdsToInclude.isEmpty()) {
            log.debug("BOM过滤: 未找到物料编号 '{}' 的匹配项", materialNo);
            return new ArrayList<>();
        }

        log.debug("BOM过滤: 过滤后找到 {} 个要包含的项目", nodeIdsToInclude.size());

        // 步骤 2: 仅使用标记的节点构建树
        return TreeUtils.buildTree(
                flatList,
                nodeIdsToInclude,
                BomItemsVO::getId,
                BomItemsVO::getDepth,
                item -> item,
                BomItemsVO::getChildren,
                BomItemsVO::setChildren);
    }

    /**
     * 根据物料编号标记要包含的节点, 包括直接匹配项、它们的祖先和它们的后代。
     *
     * @param flatList   要分析的BOM项的扁平列表
     * @param materialNo 用于过滤的物料编号 (不区分大小写的子字符串匹配)
     * @return 要在过滤后的树中包含的节点ID集合
     */
    private Set<Integer> markNodesForInclusion(List<BomItemsVO> flatList, String materialNo) {
        if (flatList == null || flatList.isEmpty() || StringUtils.isBlank(materialNo)) {
            log.debug("BOM过滤: 列表为空或物料编号为空, 返回空集合。");
            return Collections.emptySet();
        }

        log.debug("BOM过滤: 开始对物料编号 '{}' 进行过滤", materialNo);

        // 使用TreeUtils构建树操作所需的数据结构
        TreeUtils.TreeRelationships<Integer, BomItemsVO> treeRelationships = TreeUtils.buildTreeRelationships(
                flatList,
                BomItemsVO::getId,
                BomItemsVO::getDepth);

        // 根据物料编号查找直接匹配项
        Set<Integer> directMatchIds = findDirectMatches(flatList, materialNo);

        if (directMatchIds.isEmpty()) {
            log.debug("BOM过滤: 未找到物料编号 '{}' 的直接匹配项", materialNo);
            return Collections.emptySet();
        }

        log.debug("BOM过滤: 找到 {} 个直接匹配项: {}", directMatchIds.size(), directMatchIds);

        // 使用TreeUtils查找直接匹配项的祖先和后代
        Set<Integer> ancestors = TreeUtils.findAncestors(directMatchIds, treeRelationships.getChildToParentMap());
        Set<Integer> descendants = TreeUtils.findDescendants(directMatchIds,
                treeRelationships.getParentToChildrenMap());

        // 合并所有要包含的节点
        Set<Integer> nodesToInclude = new HashSet<>(directMatchIds);
        nodesToInclude.addAll(ancestors);
        nodesToInclude.addAll(descendants);

        log.debug("BOM过滤: 最终包含的节点集大小: {}", nodesToInclude.size());
        return nodesToInclude;
    }

    /**
     * 查找物料编号直接匹配搜索条件的项。
     *
     * @param flatList   BOM项的扁平列表
     * @param materialNo 要匹配的物料编号 (不区分大小写)
     * @return 直接匹配项的ID集合
     */
    private Set<Integer> findDirectMatches(List<BomItemsVO> flatList, String materialNo) {
        Set<Integer> directMatchIds = new HashSet<>();
        String lowerCaseMaterialNo = materialNo.toLowerCase();

        for (BomItemsVO item : flatList) {
            if (item.getMaterialCode() != null &&
                    item.getMaterialCode().toLowerCase().contains(lowerCaseMaterialNo)) {
                directMatchIds.add(item.getId());
            }
        }

        return directMatchIds;
    }

    /**
     * 从扁平的项目列表中构建BOM树。
     * 基于深度属性创建层级结构。
     *
     * @param flatList BOM项的扁平列表
     * @return BOM项的层级树结构
     */
    private List<BomItemsVO> buildBomTree(List<BomItemsVO> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }

        return TreeUtils.buildTree(
                flatList,
                flatList.stream().map(BomItemsVO::getId).collect(Collectors.toSet()), // Include all nodes
                BomItemsVO::getId,
                BomItemsVO::getDepth,
                item -> item,
                BomItemsVO::getChildren,
                BomItemsVO::setChildren);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadFiles(MultipartFile[] files, String productModel, String fileType, String subType,
                                  String drawingNo, String version) {
        try {
            // 创建文件上传请求对象
            FileUploadRequest request = new FileUploadRequest();
            request.setFiles(files);
            request.setProductModel(productModel);
            request.setFileType(fileType);
            request.setSubType(subType);
            request.setDrawingNo(drawingNo);
            request.setVersion(version);

            // 使用统一的文件处理服务处理请求
            return fileProcessingService.processUpload(request);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("文件上传处理失败", e);
            return AjaxResult.error("文件上传处理失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadFile(MultipartFile file, String productModel, String fileType, String subType,
                                 String drawingNo, String version) {
        // 调用多文件上传方法, 将单个文件封装为数组
        return uploadFiles(new MultipartFile[]{file}, productModel, fileType, subType, drawingNo, version);
    }

    /**
     * 保持原始文件名并添加版本号。
     */
    private String preserveOriginalFilename(String originalFilename, String version) {
        if (originalFilename == null) {
            return "unknown_file_v" + version;
        }
        // 获取文件名和扩展名
        String extension = FilenameUtils.getExtension(originalFilename);
        String baseName = FilenameUtils.getBaseName(originalFilename);
        // 添加版本号到文件名
        return baseName + "_v" + version + "." + extension;
    }

    /**
     * 处理BOM文件和物料图纸关系文件
     * @param newFile BOM文件信息
     * @param bomFilePath BOM文件路径
     * @param relationFilePath 物料图纸关系文件路径（可能为null）
     */
    private void processBomAndRelationFiles(Files newFile, Path bomFilePath, Path relationFilePath) {
        try {
            // 1. 解析BOM文件并保存
            bomItemService.parseBomExcelAndSave(newFile, bomFilePath.toString(), newFile.getSubType());

            // 2. 获取BOM项
            List<BomItemsVO> bomItems = bomItemService.selectByFileId(newFile.getId());

            // 3. 处理物料图纸关系
            if (relationFilePath != null) {
                // 3.1 如果提供了关系文件，解析并更新映射
                List<MaterialDrawingMap> relations =
                    materialDrawingRelationsService.parseRelationExcel(relationFilePath.toString(), newFile.getId());

                // 添加日志记录
                log.info("解析物料图纸关系文件成功，共解析出 {} 条关系", relations.size());

                // 批量处理物料-图纸映射关系（更新或插入）
                int successCount = materialDrawingMapService.batchUpsertFromRelations(relations);
                log.info("批量处理物料-图纸映射完成，成功率: {}/{}", successCount, relations.size());

                // 然后将关系转换为 Map 用于后续处理
                Map<String, MaterialDrawingMap> relationsMap =
                    relations.stream()
                        .collect(Collectors.toMap(MaterialDrawingMap::getMaterialCode, Function.identity(), (existing, replacement) -> existing));

                // 3.2 更新BOM项
                bomItems.forEach(bomItem -> {
                    MaterialDrawingMap relation = relationsMap.get(bomItem.getMaterialCode());
                    if (relation != null) {
                        // 可以在这里更新 BOM 项的其他信息，如果需要的话
                        // 不再需要调用 upsertMaterialDrawingMap 方法，因为我们已经在前面批量处理了所有的物料-图纸映射关系
                    }
                });
            } else {
                // 4. 如果没有提供关系文件，从现有的物料-图纸映射表中查询并填充
                log.info("未提供物料图纸关系文件，将从现有的物料-图纸映射表中查询并填充");

                // 4.1 获取所有物料编码
                List<String> materialCodes = bomItems.stream()
                    .map(BomItemsVO::getMaterialCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                if (!materialCodes.isEmpty()) {
                    // 4.2 批量查询物料-图纸映射关系
                    Map<String, com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> existingMappings =
                        materialDrawingMapService.findByMaterialCodes(materialCodes);

                    log.info("从现有映射中找到 {} 条物料-图纸映射关系", existingMappings.size());

                    // 4.3 收集需要处理的物料-图纸映射关系
                    List<com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> mappingsToProcess = new ArrayList<>();

                    for (BomItemsVO bomItem : bomItems) {
                        com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap mapping =
                            existingMappings.get(bomItem.getMaterialCode());
                        if (mapping != null) {
                            // 更新物料名称和规格，保留原有的图纸信息
                            mapping.setMaterialName(bomItem.getMaterialName());
                            mapping.setSpecification(bomItem.getSpecification());
                            mappingsToProcess.add(mapping);
                        }
                    }

                    // 4.4 批量处理物料-图纸映射关系
                    int successCount = materialDrawingMapService.batchUpsertFromRelations(mappingsToProcess);
                    log.info("从现有映射中填充了 {} 个BOM项的图纸信息", successCount);
                }
            }

            // 5. 更新BOM项
            bomItemService.updateBomItems(bomItems);
        } catch (Exception e) {
            log.error("处理BOM和物料图纸关系文件失败: {}", e.getMessage(), e);
            // 不终止整个流程, 记录错误并继续
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeFileVersion(FileChangeRequest request) {
        // 调用多文件变更方法, 将单个文件封装为数组
        if (request.getFile() != null) {
            request.setFiles(new MultipartFile[]{request.getFile()});
        }
        return changeFileVersionMultiple(request);
    }

    @Override
    public void downloadFile(Integer fileId, HttpServletResponse response) throws IOException {
        // 1. 获取文件信息并检查权限
        Files fileInfo = filesDao.fetchOneById(fileId);
        if (fileInfo == null) {
            throw new ServiceException("文件不存在");
        }

        // 检查机型权限
        // if (!fileInfo.getFileType().equals(FilesFileType.BOM_DRAWING)) {
        //     ProductModels model = productModelsService.selectByPrimaryKey(fileInfo.getProductModelId());
        // }

        // 2. 检查文件是否存在
        Path filePath = Paths.get(fileInfo.getFilePath());
        if (!java.nio.file.Files.exists(filePath)) {
            throw new ServiceException("文件不存在");
        }

        // 3. 设置响应头
        String fileName = fileInfo.getFileName();
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 4. 写入响应流
        try (InputStream inputStream = java.nio.file.Files.newInputStream(filePath);
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw new ServiceException("文件下载失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFile(Integer fileId) {
        // 检查文件信息并验证权限
        Files fileInfo = filesDao.fetchOneById(fileId);
        if (fileInfo == null) {
            throw new ServiceException("文件不存在");
        }

        // 检查机型权限
        ProductModels model = productModelsService.selectByPrimaryKey(fileInfo.getProductModelId());
        checkModelPermission(model.getModelCode());

        // 1. 删除变更记录
        FileChanges changes = new FileChanges();
        changes.setFileId(fileId);
        List<FileChanges> changesList = fileChangesDao.fetchByFileId(fileId);
        for (FileChanges change : changesList) {
            fileChangesDao.deleteById(change.getId());
        }

        // 2. 删除文件记录
        filesDao.deleteById(fileId);

        // 3. 删除BOM数据
        bomItemService.deleteBomitemsByFileId(fileId);
        return 1;
    }



    /**
     * 获取用户可访问的机型ID列表。
     */
    private Set<Integer> getUserModelIds() {
        Set<String> modelCodes = getUserModelCodes();
        if (modelCodes.isEmpty()) {
            return Collections.emptySet();
        }

        return productModelsService.selectProductModelsList(new ProductModels()).stream()
                .filter(model -> modelCodes.contains(model.getModelCode())).map(ProductModels::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 检查用户是否有指定机型的权限。
     */
    private void checkModelPermission(String modelCode) {
        if (!SecurityUtils.isAdmin() && !getUserModelCodes().contains(modelCode)) {
            // throw new ServiceException("无权操作该机型文件");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeFileVersionWithValidation(FileChangeRequest request) {
        try {
            log.info("开始执行文件版本变更操作(校验版本号)");
            // 1. 获取原文件信息并检查权限
            Files oldFile = filesDao.fetchOneById(request.getFileId());
            if (oldFile == null) {
                return AjaxResult.error("文件不存在");
            }

            // 2. 版本号校验 - 新版本必须大于当前版本
            String currentVersion = oldFile.getVersion();
            String newVersion = request.getNewVersion();

            if (currentVersion != null && newVersion != null) {
                int compareResult = VersionUtils.compareVersions(newVersion, currentVersion);
                if (compareResult <= 0) {
                    return AjaxResult.error("新版本号必须大于当前版本号: " + currentVersion);
                }
            }

            // // 3. 直接实现文件版本变更逻辑，不调用 changeFileVersionMultiple
            // // 使用请求中的 productModelId（如果有）或者原文件的 productModelId
            // Integer productModelId = request.getProductModelId() != null ? request.getProductModelId() : oldFile.getProductModelId();
            // // 检查机型权限
            // ProductModels model = productModelsService.selectByPrimaryKey(productModelId);
            // checkModelPermission(model.getModelCode());

            MultipartFile[] files = request.getFiles();
            // 验证上传文件
            if (files == null || files.length == 0) {
                return AjaxResult.error("请选择要上传的文件");
            }

            // 更新原文件状态为报废
            oldFile.setStatus(FilesStatus.OBSOLETE)
                  .setUpdatedAt(LocalDateTime.now());
            filesDao.update(oldFile);

            // 记录变更历史
            FileChanges changes = new FileChanges()
                .setFileId(oldFile.getId())
                .setOldVersion(oldFile.getVersion())
                .setNewVersion(request.getNewVersion())
                .setChangeReason(request.getChangeReason())
                .setChangedBy(SecurityUtils.getUserId().intValue())
                .setChangedAt(LocalDateTime.now());
            fileChangesDao.insert(changes);

            // 构建目标目录
            String targetDir = oldFile.getFilePath().substring(0, oldFile.getFilePath().lastIndexOf('/'));
            targetDir = targetDir.substring(0, targetDir.lastIndexOf('/')) + "/V" + request.getNewVersion();
            Path directory = Paths.get(targetDir);
            if (!java.nio.file.Files.exists(directory)) {
                java.nio.file.Files.createDirectories(directory);
            }

            // 处理BOM文件
            MultipartFile bomFile = files[0];
            if (bomFile.isEmpty()) {
                return AjaxResult.error("上传的BOM文件为空");
            }

            String bomFileName = preserveOriginalFilename(bomFile.getOriginalFilename(), request.getNewVersion());
            Path bomFilePath = directory.resolve(bomFileName);
            java.nio.file.Files.copy(bomFile.getInputStream(), bomFilePath, StandardCopyOption.REPLACE_EXISTING);

            // 创建新版本文件记录
            Files newFile = new Files();
            BeanUtils.copyProperties(oldFile, newFile);
            newFile.setId(null)
                  .setVersion(request.getNewVersion())
                  .setStatus(FilesStatus.ACTIVE)
                  .setFilePath(bomFilePath.toString())
                  .setFileName(bomFile.getOriginalFilename())
                  .setCreatedAt(null)
                  .setUpdatedAt(null);
            if (request.getProductModelId() != null) {
                newFile.setProductModelId(request.getProductModelId());
            }
            filesDao.insert(newFile);

            // 处理物料图纸关系文件（如果有）
            Path relationFilePath = null;
            if (files.length > 1 && !files[1].isEmpty()) {
                MultipartFile relationFile = files[1];
                String relationFileExtension = FilenameUtils.getExtension(relationFile.getOriginalFilename());
                String relationFileName = String.format("material_drawing_relation_v%s.%s", request.getNewVersion(), relationFileExtension);
                relationFilePath = directory.resolve(relationFileName);
                java.nio.file.Files.copy(relationFile.getInputStream(), relationFilePath, StandardCopyOption.REPLACE_EXISTING);
                log.info("物料图纸关系文件已保存: {}", relationFilePath);
            } else {
                log.info("未提供物料图纸关系文件，将只处理BOM文件");
            }

            // 如果是BOM文件, 解析Excel内容并处理物料图纸关系
            if ("BOM".equals(newFile.getFileType().name()) && newFile.getSubType() != null) {
                processBomAndRelationFiles(newFile, bomFilePath, relationFilePath);
            }

            log.info("文件版本变更操作成功完成");
            return AjaxResult.success("变更成功");
        } catch (Exception e) {
            log.error("文件版本变更失败", e);
            throw new ServiceException("文件版本变更失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeFileVersionMultiple(FileChangeRequest request) {
        try {
            log.info("开始执行多文件版本变更操作");
            // 1. 获取原文件信息并检查权限
            Files oldFile = filesDao.fetchOneById(request.getFileId());
            if (oldFile == null) {
                return AjaxResult.error("文件不存在");
            }
            // 使用请求中的 productModelId（如果有）或者原文件的 productModelId
            Integer productModelId = request.getProductModelId() != null ? request.getProductModelId() : oldFile.getProductModelId();
            // 检查机型权限
            ProductModels model = productModelsService.selectByPrimaryKey(productModelId);
            checkModelPermission(model.getModelCode());

            MultipartFile[] files = request.getFiles();
            // 2. 验证上传文件
            if (files == null || files.length == 0) {
                return AjaxResult.error("请选择要上传的文件");
            }

            // 3. 更新原文件状态为报废
            oldFile.setStatus(FilesStatus.OBSOLETE)
                  .setUpdatedAt(LocalDateTime.now());
            filesDao.update(oldFile);

            // 记录变更历史
            FileChanges changes = new FileChanges()
                .setFileId(oldFile.getId())
                .setOldVersion(oldFile.getVersion())
                .setNewVersion(request.getNewVersion())
                .setChangeReason(request.getChangeReason())
                .setChangedBy(SecurityUtils.getUserId().intValue())
                .setChangedAt(LocalDateTime.now());
            fileChangesDao.insert(changes);

            // 构建目标目录
            String targetDir = oldFile.getFilePath().substring(0, oldFile.getFilePath().lastIndexOf('/'));
            targetDir = targetDir.substring(0, targetDir.lastIndexOf('/')) + "/V" + request.getNewVersion();
            Path directory = Paths.get(targetDir);
            if (!java.nio.file.Files.exists(directory)) {
                java.nio.file.Files.createDirectories(directory);
            }

            // 处理BOM文件
            MultipartFile bomFile = files[0];
            if (bomFile.isEmpty()) {
                return AjaxResult.error("上传的BOM文件为空");
            }

            String bomFileName = preserveOriginalFilename(bomFile.getOriginalFilename(), request.getNewVersion());
            Path bomFilePath = directory.resolve(bomFileName);
            java.nio.file.Files.copy(bomFile.getInputStream(), bomFilePath, StandardCopyOption.REPLACE_EXISTING);

            // 创建新版本文件记录
            Files newFile = new Files();
            BeanUtils.copyProperties(oldFile, newFile);
            newFile.setId(null)
                  .setVersion(request.getNewVersion())
                  .setStatus(FilesStatus.ACTIVE)
                  .setFilePath(bomFilePath.toString())
                  .setFileName(bomFileName)
                  .setCreatedAt(null)
                  .setUpdatedAt(null);
            if (request.getProductModelId() != null) {
                newFile.setProductModelId(request.getProductModelId());
            }
            filesDao.insert(newFile);

            // 处理物料图纸关系文件（如果有）
            Path relationFilePath = null;
            if (files.length > 1 && !files[1].isEmpty()) {
                MultipartFile relationFile = files[1];
                String relationFileExtension = FilenameUtils.getExtension(relationFile.getOriginalFilename());
                String relationFileName = String.format("material_drawing_relation_v%s.%s", request.getNewVersion(), relationFileExtension);
                relationFilePath = directory.resolve(relationFileName);
                java.nio.file.Files.copy(relationFile.getInputStream(), relationFilePath, StandardCopyOption.REPLACE_EXISTING);
                log.info("物料图纸关系文件已保存: {}", relationFilePath);
            } else {
                log.info("未提供物料图纸关系文件，将只处理BOM文件");
            }

            // 如果是BOM文件, 解析Excel内容并处理物料图纸关系
            if ("BOM".equals(newFile.getFileType().name()) && newFile.getSubType() != null) {
                processBomAndRelationFiles(newFile, bomFilePath, relationFilePath);
            }

            log.info("文件版本变更操作成功完成");
            return AjaxResult.success("变更成功");
        } catch (Exception e) {
            log.error("文件版本变更失败", e);
            throw new ServiceException("文件版本变更失败");
        }
    }
}
