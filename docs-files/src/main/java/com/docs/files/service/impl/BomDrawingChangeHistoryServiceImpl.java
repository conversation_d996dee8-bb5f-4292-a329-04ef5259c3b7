package com.docs.files.service.impl;

import com.docs.common.core.domain.vo.BomDrawingChangeHistoryDTO;
import com.docs.common.enums.ChangeType;
import com.docs.common.enums.DrawingFileType;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.service.BomDrawingChangeHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import com.docs.common.jooq.generated.tables.daos.BomDrawingChangeHistoryDao;
import com.docs.common.jooq.generated.tables.pojos.BomDrawingChangeHistory;

import static com.docs.common.jooq.generated.Tables.BOM_DRAWING_CHANGE_HISTORY;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * BOM图纸变更历史服务实现 - 新版本
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BomDrawingChangeHistoryServiceImpl implements BomDrawingChangeHistoryService {
    
    private final DSLContext dsl;
    private final BomDrawingChangeHistoryDao bomDrawingChangeHistoryDao;
    
    @Override
    public List<BomDrawingChangeHistoryDTO> selectBomDrawingChangeHistory(Integer bomItemId) {
        log.debug("查询BOM项 {} 的图纸变更历史", bomItemId);
        
        return dsl.select()
                .from(DSL.table("bom_drawing_change_history_view"))
                .where(DSL.field("bom_item_id").eq(bomItemId))
                .orderBy(DSL.field("changed_at").desc())
                .fetchInto(BomDrawingChangeHistoryDTO.class);
    }
    
    @Override
    public List<BomDrawingChangeHistoryDTO> selectBomDrawingChangeHistoryByType(Integer bomItemId, DrawingFileType fileType) {
        log.debug("查询BOM项 {} 的 {} 类型图纸变更历史", bomItemId, fileType.getCode());

        return dsl.select()
                .from(DSL.table("bom_drawing_change_history_view"))
                .where(DSL.field("bom_item_id").eq(bomItemId))
                .and(DSL.field("file_type").eq(fileType.getCode()))
                .orderBy(DSL.field("changed_at").desc())
                .fetchInto(BomDrawingChangeHistoryDTO.class);
    }

    @Override
    public List<BomDrawingChangeHistoryDTO> selectDrawingOnlyChangeHistory(Integer bomItemId) {
        log.debug("查询BOM项 {} 的图纸变更历史（排除研发文件）", bomItemId);

        // 只查询图纸相关的文件类型，排除 RD_FILE
        return dsl.select()
                .from(DSL.table("bom_drawing_change_history_view"))
                .where(DSL.field("bom_item_id").eq(bomItemId))
                .and(DSL.field("file_type").in("PDF", "2D", "3D", "COMPRESSED"))
                .orderBy(DSL.field("changed_at").desc())
                .fetchInto(BomDrawingChangeHistoryDTO.class);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordDrawingChange(Integer bomItemId, String materialCode, DrawingFileType fileType,
                                  ChangeType changeType, String oldDrawingNo, Integer oldFileId,
                                  String oldFileName, String oldFileVersion, String newDrawingNo,
                                  Integer newFileId, String newFileName, String newFileVersion,
                                  String changeReason) {
        
        String currentUserName = SecurityUtils.getLoginUser().getUser().getNickName();
        Long currentUserId = SecurityUtils.getUserId();
        
        log.info("记录图纸变更历史: BOM项ID={}, 物料编码={}, 文件类型={}, 变更类型={}, 变更人={}",
                bomItemId, materialCode, fileType.getCode(), changeType.getCode(), currentUserName);
        
        dsl.insertInto(BOM_DRAWING_CHANGE_HISTORY)
                .set(BOM_DRAWING_CHANGE_HISTORY.BOM_ITEM_ID, bomItemId)
                .set(BOM_DRAWING_CHANGE_HISTORY.MATERIAL_CODE, materialCode)
                .set(BOM_DRAWING_CHANGE_HISTORY.CHANGE_TYPE, changeType.getCode())
                .set(BOM_DRAWING_CHANGE_HISTORY.FILE_TYPE, fileType.getCode())
                .set(BOM_DRAWING_CHANGE_HISTORY.OLD_DRAWING_NO, oldDrawingNo)
                .set(BOM_DRAWING_CHANGE_HISTORY.OLD_FILE_ID, oldFileId)
                .set(BOM_DRAWING_CHANGE_HISTORY.OLD_FILE_NAME, oldFileName)
                .set(BOM_DRAWING_CHANGE_HISTORY.OLD_FILE_VERSION, oldFileVersion)
                .set(BOM_DRAWING_CHANGE_HISTORY.NEW_DRAWING_NO, newDrawingNo)
                .set(BOM_DRAWING_CHANGE_HISTORY.NEW_FILE_ID, newFileId)
                .set(BOM_DRAWING_CHANGE_HISTORY.NEW_FILE_NAME, newFileName)
                .set(BOM_DRAWING_CHANGE_HISTORY.NEW_FILE_VERSION, newFileVersion)
                .set(BOM_DRAWING_CHANGE_HISTORY.CHANGE_REASON, changeReason)
                .set(BOM_DRAWING_CHANGE_HISTORY.CHANGED_BY, currentUserId)
                .set(BOM_DRAWING_CHANGE_HISTORY.CHANGED_BY_NAME, currentUserName)
                .set(BOM_DRAWING_CHANGE_HISTORY.CHANGED_AT, LocalDateTime.now())
                .execute();
        
        log.debug("图纸变更历史记录成功");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordMultipleDrawingChanges(Integer bomItemId, String materialCode,
                                           List<DrawingChangeInfo> changes, String changeReason) {
        
        if (changes == null || changes.isEmpty()) {
            log.warn("变更信息列表为空，跳过记录");
            return;
        }
        
        String currentUserName = SecurityUtils.getLoginUser().getUser().getNickName();
        Long currentUserId = SecurityUtils.getUserId();
        LocalDateTime changeTime = LocalDateTime.now();
        
        log.info("批量记录图纸变更历史: BOM项ID={}, 物料编码={}, 变更数量={}, 变更人={}",
                bomItemId, materialCode, changes.size(), currentUserName);
        
        // 逐个插入变更记录
        int recordsInserted = 0;
        for (DrawingChangeInfo change : changes) {
            int result = dsl.insertInto(BOM_DRAWING_CHANGE_HISTORY)
                    .set(BOM_DRAWING_CHANGE_HISTORY.BOM_ITEM_ID, bomItemId)
                    .set(BOM_DRAWING_CHANGE_HISTORY.MATERIAL_CODE, materialCode)
                    .set(BOM_DRAWING_CHANGE_HISTORY.CHANGE_TYPE, change.getChangeType().getCode())
                    .set(BOM_DRAWING_CHANGE_HISTORY.FILE_TYPE, change.getFileType().getCode())
                    .set(BOM_DRAWING_CHANGE_HISTORY.OLD_DRAWING_NO, change.getOldDrawingNo())
                    .set(BOM_DRAWING_CHANGE_HISTORY.OLD_FILE_ID, change.getOldFileId())
                    .set(BOM_DRAWING_CHANGE_HISTORY.OLD_FILE_NAME, change.getOldFileName())
                    .set(BOM_DRAWING_CHANGE_HISTORY.OLD_FILE_VERSION, change.getOldFileVersion())
                    .set(BOM_DRAWING_CHANGE_HISTORY.NEW_DRAWING_NO, change.getNewDrawingNo())
                    .set(BOM_DRAWING_CHANGE_HISTORY.NEW_FILE_ID, change.getNewFileId())
                    .set(BOM_DRAWING_CHANGE_HISTORY.NEW_FILE_NAME, change.getNewFileName())
                    .set(BOM_DRAWING_CHANGE_HISTORY.NEW_FILE_VERSION, change.getNewFileVersion())
                    .set(BOM_DRAWING_CHANGE_HISTORY.CHANGE_REASON, changeReason)
                    .set(BOM_DRAWING_CHANGE_HISTORY.CHANGED_BY, currentUserId)
                    .set(BOM_DRAWING_CHANGE_HISTORY.CHANGED_BY_NAME, currentUserName)
                    .set(BOM_DRAWING_CHANGE_HISTORY.CHANGED_AT, changeTime)
                    .execute();
            recordsInserted += result;
        }
        log.info("批量记录图纸变更历史完成，共插入 {} 条记录", recordsInserted);
    }
}